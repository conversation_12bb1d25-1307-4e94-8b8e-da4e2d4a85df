/**
 * 全局筛选条件 Context
 */

import React, { createContext, useContext, useState, ReactNode } from 'react';
import type { FilterOptions } from '@/types';

// 定义筛选条件的类型从全局 types 引入

interface FilterContextType {
  filterOptions: FilterOptions;
  setFilterOptions: (options: FilterOptions) => void;
  updateFilterOptions: (newOptions: Partial<FilterOptions>) => void;
}

const FilterContext = createContext<FilterContextType | undefined>(undefined);

interface FilterProviderProps {
  children: ReactNode;
}

export const FilterProvider: React.FC<FilterProviderProps> = ({ children }) => {
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({});

  const updateFilterOptions = (newOptions: Partial<FilterOptions>) => {
    setFilterOptions((prev) => ({ ...prev, ...newOptions }));
  };

  const value = {
    filterOptions,
    setFilterOptions,
    updateFilterOptions,
  };

  return <FilterContext.Provider value={value}>{children}</FilterContext.Provider>;
};

export const useFilterOptions = (): FilterContextType => {
  const context = useContext(FilterContext);
  if (context === undefined) {
    throw new Error('useFilterOptions must be used within a FilterProvider');
  }
  return context;
};
