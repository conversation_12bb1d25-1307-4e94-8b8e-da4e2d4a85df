import React, { useEffect, useState } from 'react';
import { ShopSelect } from '../shop-select';
import './index.less';
import { getBaseRequestParam, isAmapAgent, sendEvent } from '@/common/utils';
import { TaskCard } from '../task-card';
import { Button, Empty, Result, Row, message } from 'antd';
import { TASK_DETAIL_TABS, TASK_STATUS, PAGE_STATUS } from '@/common/const';
import service from '@/_docplus/target/service/amap-sales-operation-client/AgentOperationQueryFacade';
import {
  AgentOperationDetailDTO,
  TaskDetailDTO,
  AgentOperationShopRelationDTO,
} from '@/_docplus/target/types/amap-sales-operation-client';
import { useStore } from '@/context/global-store';
import PurchaseRequestForm from './PurchaseRequestForm';
import MerchantAuditForm from './components/MerchantAuditForm';
import { Module<PERSON><PERSON><PERSON><PERSON>, PageSPMKey, traceExp } from '@/utils/trace';

const filterDetailList = (taskDetailDTOList: any[]) => {
  const incompleteData = (taskDetailDTOList || []).filter(
    (item) => item.taskDetailStatus === TASK_STATUS.INCOMPLETE,
  );
  const completeData = (taskDetailDTOList || []).filter(
    (item) => item.taskDetailStatus === TASK_STATUS.COMPLETED,
  );
  return [incompleteData, completeData];
};

interface IProps {
  pid: string;
  visible: boolean;
  jumpSource: string;
  shop?: AgentOperationDetailDTO;
  shopList: AgentOperationShopRelationDTO[];
  data: any;
}
export const ShopInfrastructTaskDetail: React.FC<IProps> = ({
  pid,
  shopList: shops,
  data,
  visible,
  jumpSource,
  shop: defaultShop,
}) => {
  const [shopList, setShopList] = useState(shops);
  const [shop, setShop] = useState<AgentOperationDetailDTO>(defaultShop);
  const { taskDetailDTOList, shopScoreLevel } = data || {};
  const [status, setStatus] = useState<PAGE_STATUS>(
    taskDetailDTOList?.length > 0 ? PAGE_STATUS.SUCCESS : PAGE_STATUS.EMPTY,
  );
  const listData = filterDetailList(taskDetailDTOList);
  const [incompleteList, setIncompleteList] = useState<TaskDetailDTO[] | null>(listData[0]);
  const [completeList, setCompleteList] = useState<TaskDetailDTO[] | null>(listData[1]);
  const is33 = !isAmapAgent();
  useEffect(() => {
    if (visible) {
      if (status === PAGE_STATUS.EMPTY) {
        fetchList(shop);
      }
      sendEvent(TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK, 'EXP');
      traceExp(PageSPMKey.首页, ModuleSPMKey['基建任务.模块曝光'], {
        shopId: shop?.shopId,
      });
    } else {
      setStatus(PAGE_STATUS.EMPTY);
    }
  }, [visible]);

  useEffect(() => {
    // 解决从商户列表跳转，首次使用接口返回门店，拿不到商家分问题
    if (shopList?.length > 0 && shop && shop.shopQualityScore === null) {
      const currentShop = shopList.find((item) => item.shopId === shop?.shopId);
      if (currentShop) {
        setShop(currentShop);
      }
    }
  }, [shopList]);

  const { viewer } = useStore();
  const fetchList = (shop: AgentOperationDetailDTO) => {
    const { shopId } = shop || {};
    if (!shopId) {
      return;
    }
    setStatus(PAGE_STATUS.LOADING);
    const param = {
      viewOperatorId: viewer || undefined,
      taskDetailScene: TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK,
      pid,
      jumpSource,
      shopId,
      ...getBaseRequestParam(),
    };
    service
      .queryAgentOperationDetail(param)
      .then((res) => {
        const { success, data, msgInfo: resultMessage } = res || {};
        if (!success) {
          message.error(resultMessage);
          setStatus(PAGE_STATUS.ERROR);
          return;
        }
        if (!data) {
          setStatus(PAGE_STATUS.EMPTY);
          return;
        }
        const { taskDetailDTOList } = data || {};

        if (taskDetailDTOList?.length > 0) {
          const [incompleteData, completeData] = filterDetailList(taskDetailDTOList);
          setIncompleteList(incompleteData);
          setCompleteList(completeData);
          setStatus(PAGE_STATUS.SUCCESS);
        } else {
          setStatus(PAGE_STATUS.EMPTY);
        }
      })
      .catch((e) => {
        console.error('任务详情请求失败: ', e);
        message.error(e?.res?.resultMsg);
        setStatus(PAGE_STATUS.ERROR);
      });
  };

  const handleShopChange = (shop: AgentOperationDetailDTO) => {
    setShop(shop);
    fetchList(shop);
  };

  const handleShopList = (value: AgentOperationDetailDTO[]) => {
    setShopList(value);
    if (value?.length > 0 && !shop) {
      setShop(value[0]);
      if (status === PAGE_STATUS.EMPTY) {
        fetchList(value[0]);
      }
    }
  };

  return (
    <div className="task-tab-container">
      <div className="action-container">
        <ShopSelect
          pid={pid}
          shop={shop}
          shopList={shopList}
          tabKey={TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK}
          onShopChange={handleShopChange}
          onShopList={handleShopList}
        />
      </div>
      <PurchaseRequestForm shopId={shop?.shopId} />
      {/* 提报审核 */}
      {is33 && <MerchantAuditForm shopId={shop?.shopId} />}

      {status === PAGE_STATUS.SUCCESS && (
        <div>
          {Array.isArray(incompleteList) && (
            <div
              className="task-title"
              style={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <span>未完成任务（{incompleteList?.length}项）</span>
              {shopScoreLevel && (
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <span style={{ marginRight: 8 }}>当前等级:</span>
                  <span
                    style={{
                      fontWeight: 500,
                      padding: '2px 8px',
                      borderRadius: 4,
                    }}
                  >
                    {shopScoreLevel}
                  </span>
                </div>
              )}
            </div>
          )}
          {incompleteList?.length > 0 && (
            <Row gutter={[16, 16]} style={{ marginTop: '8px' }}>
              {incompleteList.map((detail, index) => (
                <TaskCard
                  detailInfo={detail}
                  tabKey={TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK}
                  key={index}
                  shopId={shop?.shopId}
                />
              ))}
            </Row>
          )}
          {Array.isArray(completeList) && (
            <div className="task-title">已完成任务（{completeList?.length}项）</div>
          )}
          {completeList?.length > 0 && (
            <Row gutter={[16, 16]} style={{ marginTop: '8px' }}>
              {completeList.map((detail, index) => (
                <TaskCard
                  detailInfo={detail}
                  tabKey={TASK_DETAIL_TABS.SHOP_INFRASTRUCT_TASK}
                  key={index}
                  shopId={shop?.shopId}
                />
              ))}
            </Row>
          )}
        </div>
      )}
      {status === PAGE_STATUS.EMPTY && <Empty style={{ margin: '16px' }} />}
      {status === PAGE_STATUS.ERROR && (
        <Result
          className="task-result"
          title="网络出错了"
          subTitle="网络开小差了，请检查网络连接后重试"
          extra={
            <Button
              type="primary"
              onClick={() => {
                fetchList(shop);
              }}
            >
              点击重试
            </Button>
          }
        />
      )}
    </div>
  );
};
