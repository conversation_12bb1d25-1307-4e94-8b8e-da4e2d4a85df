import React from 'react';
import { Card, Tag, Tooltip } from 'antd';
import styled from 'styled-components';
import { CopyOutlined } from '@ant-design/icons';
import TaskCountdown from '@/pages/index/components/todo-list/TaskCountdown';

const StyledCard = styled(Card)`
  border-radius: 2px;
  background: linear-gradient(180deg, rgba(255, 232, 234, 0.4) 0%, rgba(255, 255, 255, 0) 99%);

  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .ant-card-body {
    padding: 16px;
  }
`;

const CountdownContainer = styled.div`
  width: 257px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  gap: 3px;
  z-index: 5;

  .time-number {
    color: #ff4d4f;
    font-weight: 500;
  }
`;

const MerchantNameContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  justify-content: space-between;
`;

const MerchantName = styled.div`
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: normal;
  color: rgba(0, 0, 0, 0.85);
`;

const RecommendTag = styled(Tag)`
  padding: 1px 8px;
  gap: 3px;
  z-index: 4;
  border-radius: 2px;

  &.supervisor_urge {
    background: #fff1f0;
    color: #f5222d;
  }

  &.smart_recommend {
    background: #fff7e6;
    color: #fa8c16;
  }
`;

const PidContainer = styled.div`
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    color: #1890ff;
  }

  .copy-icon {
    margin-left: 4px;
    opacity: 0.6;
  }
`;

const TaskInfo = styled.div`
  font-size: 14px;
  color: #595959;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const RevenueInfo = styled.div`
  background: #f6ffed;
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 10px;
  background: linear-gradient(270deg, #edf9f3 0%, #eef8fe 100%);
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
`;

export interface IMerchantRecommendationCardProps {
  /** 倒计时时间戳 */
  countdownTime?: number;
  /** 商户名称 */
  merchantName: string;
  /** 推荐标签类型 */
  recommendTagType?: 'SUPERVISOR_URGE' | 'SMART_RECOMMEND' | 'SUPERVISOR_ASSIGN';
  /** 商户PID */
  pid: string;
  /** 任务信息 */
  taskInfo: string;
  /** 门店数量 */
  storeCount?: number;
  /** 预估收益 */
  estimatedRevenue?: string;
  /** 点击事件 */
  onClick?: () => void;
  /** 复制PID事件 */
  onCopyPid?: (pid: string) => void;
}

const MerchantRecommendationCard: React.FC<IMerchantRecommendationCardProps> = ({
  countdownTime,
  merchantName,
  recommendTagType,
  pid,
  taskInfo,
  storeCount,
  estimatedRevenue,
  onClick,
  onCopyPid,
}) => {
  const getRecommendTagText = (type: string) => {
    switch (type) {
      case 'SUPERVISOR_URGE':
        return '主管催办';
      case 'SUPERVISOR_ASSIGN':
        return '主管下达';
      case 'SMART_RECOMMEND':
        return '智能推荐';
      default:
        return '';
    }
  };

  const handleCopyPid = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onCopyPid) {
      onCopyPid(pid);
    } else {
      navigator.clipboard.writeText(pid);
    }
  };

  return (
    <StyledCard hoverable onClick={onClick}>
      {countdownTime && (
        <CountdownContainer>
          <TaskCountdown
            expireTime={countdownTime}
            style={{
              background: 'transparent',
              padding: 0,
              borderRadius: 0,
              color: 'inherit',
            }}
          />
        </CountdownContainer>
      )}

      <MerchantNameContainer>
        <Tooltip title={merchantName}>
          <MerchantName>{merchantName}</MerchantName>
        </Tooltip>
        {recommendTagType && (
          <RecommendTag className={recommendTagType.toLowerCase()}>
            {getRecommendTagText(recommendTagType)}
          </RecommendTag>
        )}
      </MerchantNameContainer>

      <PidContainer onClick={handleCopyPid}>
        PID: {pid}
        <CopyOutlined className="copy-icon" />
      </PidContainer>

      <TaskInfo>
        <span>{taskInfo}</span>
        {storeCount && <span>{storeCount}个门店</span>}
      </TaskInfo>

      {estimatedRevenue && <RevenueInfo>{estimatedRevenue}</RevenueInfo>}
    </StyledCard>
  );
};

export default MerchantRecommendationCard;
