import React, { useEffect, useState } from 'react';
import { Card, Flex, Typography, Spin } from 'antd';
import MerchantRecommendationCard from '@/components/merchant-recommendation-card';
import styled from 'styled-components';
import { useRequest } from 'ahooks';
import { queryTodayRecommendTaskInfo } from '@/services';

const { Title } = Typography;

const RecommendationSection = styled.div`
  margin-bottom: 24px;
`;

const RecommendationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

export default function MerchantRecommendation() {
  const [recommendationData, setRecommendationData] = useState<any[]>([]);

  const { data: recommendTaskData, loading } = useRequest(async () => {
    // 获取当前运维小二ID

    const result = await queryTodayRecommendTaskInfo();

    return result;
  });

  useEffect(() => {
    if (recommendTaskData && Array.isArray(recommendTaskData)) {
      const processedData = recommendTaskData.map((item) => ({
        countdownTime: item.recommendExpiredTime,
        merchantName: item.merchantName,
        recommendTagType: getRecommendTagType(item.recommendType),
        pid: item.merchantId,
        taskInfo: item.taskName,
        storeCount: item.shopCount,
        estimatedRevenue: item.incomeValueText,
      }));

      setRecommendationData(processedData);
    } else {
      setRecommendationData([]);
    }
  }, [recommendTaskData]);

  const getRecommendTagType = (recommendType: string) => {
    const typeMap: Record<string, string> = {
      SYSTEM: 'SMART_RECOMMEND',
      REMIND: 'SUPERVISOR_URGE',
      ARRANGE: 'SUPERVISOR_ASSIGN',
    };
    return typeMap[recommendType] || 'SMART_RECOMMEND';
  };

  const handleRecommendationClick = (merchantName: string) => {
    // eslint-disable-next-line no-console
    console.log('点击了推荐商家:', merchantName);
  };

  const handleCopyPid = (pid: string) => {
    navigator.clipboard.writeText(pid);
    // eslint-disable-next-line no-console
    console.log('复制了PID:', pid);
  };

  return (
    <Card>
      <RecommendationSection>
        <RecommendationHeader>
          <Title level={4} style={{ margin: 0 }}>
            今日建议沟通商家
          </Title>
        </RecommendationHeader>
        <Spin spinning={loading}>
          {recommendationData.length > 0 ? (
            <Flex gap={16} style={{ width: '100%', overflowX: 'auto' }}>
              {recommendationData.map((item) => (
                <div key={item.pid} style={{ flex: '0 0 280px' }}>
                  <MerchantRecommendationCard
                    countdownTime={item.countdownTime}
                    merchantName={item.merchantName}
                    recommendTagType={item.recommendTagType}
                    pid={item.pid}
                    taskInfo={item.taskInfo}
                    storeCount={item.storeCount}
                    estimatedRevenue={item.estimatedRevenue}
                    onClick={() => handleRecommendationClick(item.merchantName)}
                    onCopyPid={handleCopyPid}
                  />
                </div>
              ))}
            </Flex>
          ) : (
            !loading && (
              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                暂无推荐商家数据
              </div>
            )
          )}
        </Spin>
      </RecommendationSection>
    </Card>
  );
}
