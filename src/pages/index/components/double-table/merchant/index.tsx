import MerchantSearchForm from './searchform';
import MerchantTable from './merchant-table';
import { Form } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { useAntdTable } from 'ahooks';
import { getMerchantList } from '@/services/ai-material';
import { MERCHANT_SORT_TYPE_STATUS, SORT_BY_STATUS, TASK_TABPAN_ENUM } from '@/common/const';
import { useStore } from '@/context/global-store';
import { traceExp, PageSPMKey, ModuleSPMKey, traceClick } from '@/utils/trace';
import emitter from '@/utils/emitters';
import { EmitterEventMap } from '@/utils/emitters/enum';
import { useDoubleTableStore } from '../double-table-store';
import { useTaskQueue } from '../hooks/useTaskQueue';

const defaultPageSize = 10;

interface MerchantRef {
  search: (params?: any) => Promise<void>;
  form: any;
  openAlertTaskDrawer: (data: any) => void;
}

interface MerchantProps {}

const Merchant = forwardRef<MerchantRef, MerchantProps>((props, ref) => {
  const [form] = Form.useForm();
  const { viewer } = useStore();
  const { isExpanded } = useDoubleTableStore();
  const merchantTableRef = useRef<any>(null);

  // 使用 hooks 处理商户任务队列
  const { hasTask } = useTaskQueue(TASK_TABPAN_ENUM.MERCHANT);

  const { tableProps, runAsync: fetchList } = useAntdTable(
    async (page, _params: any = form.getFieldsValue()) => {
      let warningTaskLabels;
      if (_params.warningTaskLabels) {
        warningTaskLabels = Array.isArray(_params.warningTaskLabels)
          ? _params.warningTaskLabels
          : [_params.warningTaskLabels];
      }

      const params = {
        viewOperatorId: viewer || undefined,
        page: {
          pageNo: page.current,
          pageSize: page.pageSize,
        },
        source: 'BASE_MERCHANT_LIST',
        ..._params,
        warningTaskLabels,
        taskRecommendSource: _params.taskRecommendSource,
        storeTier: _params.storeTier,
        hasNoGroup: _params.hasNoGroup,
      };

      if (page.sorter) {
        const { sorter } = page;
        const { order, field } = sorter;
        let sortBy = '';
        let sortType = '';
        if (order === 'ascend') {
          sortType = MERCHANT_SORT_TYPE_STATUS.ASC;
          if (field === 'adCurrentMonthCost') {
            sortBy = SORT_BY_STATUS.MONTHCOST;
          }
          if (field === 'adCurrentBalance') {
            sortBy = SORT_BY_STATUS.CURRENTBALANCE;
          }
        }
        if (order === 'descend') {
          sortType = MERCHANT_SORT_TYPE_STATUS.DESC;
          if (field === 'adCurrentMonthCost') {
            sortBy = SORT_BY_STATUS.MONTHCOST;
          }
          if (field === 'adCurrentBalance') {
            sortBy = SORT_BY_STATUS.CURRENTBALANCE;
          }
        }
        params.sortBy = sortBy;
        params.sortType = sortType;
      }

      const res = await getMerchantList(params);
      return {
        list: res.dataList || [],
        total: res.pageInfo?.totalCount || 0,
      };
    },
    {
      form,
      defaultPageSize,
      manual: true,
      onSuccess: (res) => {
        for (const item of res.list) {
          traceExp(PageSPMKey.首页, ModuleSPMKey['商户列表.商户曝光'], {
            pid: item.pid,
            taskNo: item.priorityTaskInfo?.taskNo || '',
          });
        }
      },
    },
  );

  const handleSearch = (params?: any) => {
    return fetchList({ current: 1, pageSize: defaultPageSize }, params);
  };

  useImperativeHandle(
    ref,
    () => ({
      search: async (params?: any) => {
        if (params) {
          // 设置表单值
          form.resetFields();
          form.setFieldsValue(params);
          // 使用传入的参数进行搜索
          await handleSearch(params);
        } else {
          await handleSearch();
        }
      },
      form,
      openAlertTaskDrawer: (data: any) => {
        if (merchantTableRef.current) {
          merchantTableRef.current.openAlertTaskDrawer(data);
        }
      },
    }),
    [fetchList, form],
  );

  const handleSwithShopList = (params: any) => {
    // 发送事件切换到门店列表，传递pid参数
    emitter.emit(EmitterEventMap.TaskDataClick, {
      type: TASK_TABPAN_ENUM.SHOP,
      params: {
        ppid: params.pid, // 将商户的pid作为门店搜索的ppid
      },
    });
  };
  const handleFormValuesChange = (changedValues: any) => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['商户列表.筛选'], {
      filterType: Object.keys(changedValues).join(','),
    });
  };

  useEffect(() => {
    if (!hasTask) {
      handleSearch();
    }
  }, []);

  return (
    <div>
      <Form form={form} onValuesChange={handleFormValuesChange}>
        <MerchantSearchForm
          onSearch={handleSearch}
          form={form}
          isShow={isExpanded}
          loading={tableProps.loading}
        />

        <MerchantTable
          ref={merchantTableRef}
          list={tableProps.dataSource || []}
          onSearch={handleSearch}
          pagination={tableProps.pagination}
          loading={tableProps.loading}
          onChange={tableProps.onChange}
          onSwithShopList={handleSwithShopList}
        />
      </Form>
    </div>
  );
});

Merchant.displayName = 'Merchant';

export default Merchant;
