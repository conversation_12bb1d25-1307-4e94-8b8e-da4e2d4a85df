import { Button, Input, Select, Form, FormInstance, Flex, DatePicker } from 'antd';
import { createPortal } from 'react-dom';
import { useMemo } from 'react';
import {
  MERCHANT_EXPIRE_CONDITION_ENUM,
  MERCHANT_EXPIRE_CONDITION_ENUM_OPTIONS,
  SHOP_SCORE_LEVEL_OPTIONS,
  STORE_TIER_OPTIONS,
} from '@/common/const';
import { MerchantSelect } from '@/components/merchant-select';
import { AgentOperationMerchantRelationDTO } from '@/_docplus/target/types/amap-sales-operation-client';
import { useMount } from 'ahooks';

const FormItem = Form.Item;
const { Option } = Select;

export interface IMerchantData {
  pid: string;
  merchantName: string;
}
interface IProps {
  onSearch: (params?: any) => void;
  form: FormInstance;
  isShow?: boolean;
  loading?: boolean;
  onFormReset?: () => void;
}
export default function SearchForm(props: IProps) {
  const { onSearch, form, isShow = true, loading = false, onFormReset } = props;

  function handleReset() {
    form.resetFields();
    onFormReset?.();
    onSearch({});
  }

  function handleMerchantChange(merchant: AgentOperationMerchantRelationDTO) {
    const { pid, merchantName } = merchant || {};
    form.setFieldsValue({ pid, merchantName });
  }

  useMount(() => {
    // 组件挂载完成
  });

  // Portal内容 - 使用useMemo优化性能
  const portalContent = useMemo(() => {
    const portalContainer = document.getElementById('search-form-portal');
    if (!portalContainer) return null;
    return createPortal(
      <Flex align="center" gap={8}>
        <Form.Item label="PID" name="ppid" style={{ marginBottom: 0 }}>
          <Input placeholder="请输入" allowClear />
        </Form.Item>
        <Form.Item label="门店ID" name="shopId" style={{ marginBottom: 0 }}>
          <Input placeholder="请输入" allowClear />
        </Form.Item>
        <Form.Item label="POIID" name="poiId" style={{ marginBottom: 0 }}>
          <Input placeholder="请输入" allowClear />
        </Form.Item>
        {!isShow && (
          <>
            <Button onClick={handleReset} disabled={loading}>
              重置
            </Button>
            <Button type="primary" onClick={() => onSearch()} loading={loading}>
              查询
            </Button>
          </>
        )}
      </Flex>,
      portalContainer,
    );
  }, [isShow, loading]);

  if (!isShow) return <>{portalContent}</>;

  return (
    <>
      {portalContent}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '5px 12px' }}>
        <FormItem name="pid" label="商户名称">
          <MerchantSelect onSelectChange={handleMerchantChange} />
        </FormItem>
        <FormItem hidden name="merchantName" />
        <FormItem label="门店名称" name="shopName">
          <Input placeholder="请输入" allowClear />
        </FormItem>
        {/* 根据运维端到端优化需求[第一批]要求，暂时注释掉任务状态筛选器 */}
        {/* <FormItem label="任务状态" name="shopTaskStatus">
          <Select placeholder="请选择">
            <Option value={TASK_STATUS.ALL}>全部</Option>
            <Option value={TASK_STATUS.COMPLETED}>已完成</Option>
            <Option value={TASK_STATUS.INCOMPLETE}>未完成</Option>
          </Select>
        </FormItem> */}
        <FormItem label="基建过期时间" name="taskExpireTime">
          <DatePicker.RangePicker
            placeholder={['开始时间', '结束时间']}
            style={{ width: '100%' }}
          />
        </FormItem>
        {/* <FormItem label="门店商家分" name="shopScoreCondition">
          <NumberRanger max={100} step={0.1} precision={1.1} allowEqual />
          {/* <Select placeholder="请选择" allowClear>
            {SHOP_SCORE_CONDITION_OPTIONS.map((item) => (
              <Option value={item.value} key={item.value}>
                {item.title}
              </Option>
            ))}
          </Select> */}
        {/* </FormItem> */}
        <FormItem label="续签状态" name="shangHuTongExpireCondition">
          <Select placeholder="请选择" mode="multiple" allowClear>
            {MERCHANT_EXPIRE_CONDITION_ENUM_OPTIONS.filter((item) => {
              return ![
                MERCHANT_EXPIRE_CONDITION_ENUM.RENEWALING,
                MERCHANT_EXPIRE_CONDITION_ENUM.RENEWALSUCCESS,
              ].includes(item.value);
            }).map((item) => (
              <Option value={item.value} key={item.value}>
                {item.title}
              </Option>
            ))}
          </Select>
        </FormItem>
        {/* <FormItem label="商户意向" name="merchantIntentionCondition">
          <Select placeholder="请选择" mode="multiple" allowClear>
            {MERCHANT_INTENTION_OPTIONS.map((item) => (
              <Option value={item.value} key={item.value}>
                {item.title}
              </Option>
            ))}
          </Select>
        </FormItem> */}
        {/* <FormItem label="门店标签" name="shopLabelCondition">
          <Select placeholder="请选择" mode="multiple" allowClear>
            {SHOP_LABELS.map((item) => (
              <Option value={item.value} key={item.value}>
                {item.title}
              </Option>
            ))}
          </Select>
        </FormItem> */}
        <FormItem label="成交方式" name="signChannel">
          <Select
            placeholder="请选择"
            allowClear
            options={[
              { label: '线上', value: 'ONLINE' },
              { label: '渠道', value: 'BD' },
            ]}
          />
        </FormItem>
        <FormItem label="商家分等级" name="shopScoreLevels">
          <Select
            mode="multiple"
            allowClear
            placeholder="请选择"
            options={SHOP_SCORE_LEVEL_OPTIONS.map((item) => ({
              value: item.value,
              label: item.title,
            }))}
          />
        </FormItem>
        <FormItem label="门店分层" name="shopOperateStage">
          <Select
            allowClear
            placeholder="请选择"
            options={STORE_TIER_OPTIONS.map((item) => ({
              value: item.value,
              label: item.title,
            }))}
          />
        </FormItem>
      </div>

      <Form.Item hidden name="shopScoreCheckboxList" initialValue={[]} />
      <Flex justify="flex-end" align="center" gap={8}>
        <Button onClick={() => handleReset()} disabled={loading}>
          重置
        </Button>
        <Button type="primary" onClick={() => onSearch()} loading={loading}>
          查询
        </Button>
      </Flex>
    </>
  );
}
