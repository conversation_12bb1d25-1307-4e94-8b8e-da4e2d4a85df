import ShopSearchForm from './search-form';
import { ShopTable } from './table';
import { Form } from 'antd';
import { forwardRef, useImperativeHandle, useState, useEffect, useRef } from 'react';
import { useQuery } from '@/hooks/useQuery';
import dayjs from 'dayjs';
import {
  TASK_STATUS,
  SORT_BY_STATUS,
  SHOP_SORT_TYPE_STATUS,
  SHOP_QUERY_SOURCE_ENUM,
  TASK_TABPAN_ENUM,
} from '@/common/const';
import { getBaseRequestParam, getNewBaseListRequestParam, sendEvent } from '@/common/utils';
import { useStore } from '@/context/global-store';
import { useAntdTable } from 'ahooks';
import { useDoubleTableStore } from '../double-table-store';
import { useTaskQueue } from '../hooks/useTaskQueue';
import { traceClick, PageSPMKey, ModuleSPMKey } from '@/utils/trace';
import { getShopList } from '@/services/ai-material';
import BatchSubmitModal from './components/batch-submit-modal';

const defaultPageSize = 10;

interface ShopRef {
  search: (params?: any) => Promise<void>;
  form: any;
  openTaskDetailDrawer: (data: any) => void;
}

interface ShopProps {}

const Shop = forwardRef<ShopRef, ShopProps>((props, ref) => {
  const [form] = Form.useForm();
  const [urlQuery] = useQuery();
  const [selectedRowKey, setSelectedRowKey] = useState(null);
  const [sortData, setSortData] = useState<{ sortBy?: any; sortType?: any }>({});
  const { viewer } = useStore();
  const { isExpanded } = useDoubleTableStore();
  const shopTableRef = useRef<any>(null);
  const [selectedLabels, setSelectedLabels] = useState<string[]>([]);

  // 使用 hooks 处理门店任务队列
  const { hasTask } = useTaskQueue(TASK_TABPAN_ENUM.SHOP);

  const { tableProps, runAsync: fetchList } = useAntdTable(
    async (page, _params: any) => {
      const { current: currentPage = 1, pageSize: size = defaultPageSize, sorter } = page;
      const values = _params || form.getFieldsValue();
      const _selectedLabels = _params ? _params.shopLabelCondition || [] : selectedLabels;

      // 请求列表清空高亮展示的点击行
      setSelectedRowKey(null);

      const params = values;
      if (_selectedLabels.length) {
        params.shopLabelCondition = _selectedLabels;
      } else {
        delete params.shopLabelCondition;
      }
      let { remainDays } = values;
      if ((remainDays as number[])?.length === 2) {
        remainDays = {
          taskOperateExpireDaysStart: remainDays[0],
          taskOperateExpireDaysEnd: remainDays[1],
        };
      } else {
        remainDays = {};
      }

      // 处理基建任务过期时间筛选
      let taskExpireStartTime;
      let taskExpireEndTime;
      if (params.taskExpireTime?.length === 2) {
        taskExpireStartTime = dayjs(params.taskExpireTime[0]).format('YYYY-MM-DD 00:00:00');
        taskExpireEndTime = dayjs(params.taskExpireTime[1]).format('YYYY-MM-DD 23:59:59');
        delete params.taskExpireTime;
      }

      // 移除门店商家分筛选逻辑
      // if (params.shopScoreCondition?.length) {
      //   params.shopScoreStart = params.shopScoreCondition[0];
      //   params.shopScoreEnd = params.shopScoreCondition[1];
      //   delete params.shopScoreCondition;
      // }
      delete params.remainDays;

      // 处理排序参数
      let sortParam = {};
      if (sorter) {
        const { order, field } = sorter;
        let sortBy = '';
        let sortType = '';
        if (order === 'ascend') {
          sortType = SHOP_SORT_TYPE_STATUS.ASC;
          if (field === 'shopQualityScore') {
            sortBy = SORT_BY_STATUS.QUANTITYSCORE;
          }
          if (field === 'signEffectiveTime') {
            sortBy = SORT_BY_STATUS.SIGN_EFFECTIVE_TIME;
          }
        }
        if (order === 'descend') {
          sortType = SHOP_SORT_TYPE_STATUS.DESC;
          if (field === 'shopQualityScore') {
            sortBy = SORT_BY_STATUS.QUANTITYSCORE;
          }
          if (field === 'signEffectiveTime') {
            sortBy = SORT_BY_STATUS.SIGN_EFFECTIVE_TIME;
          }
        }
        sortParam = { sortBy, sortType };
        setSortData({ sortBy, sortType });
      }

      const listParam = {
        viewOperatorId: viewer || undefined,
        includeShopTaskProcess: true,
        ...params,
        ppid: undefined,
        pid: params.ppid ? params.ppid : params.pid || undefined,
        merchantName: params.ppid ? undefined : params.merchantName,
        ...sortParam,
        ...remainDays,
        taskExpireEndTime,
        taskExpireStartTime,
        shopScoreLevels: params.shopScoreLevels,
        shopOperateStage: params.shopOperateStage,
        ...getNewBaseListRequestParam(size, currentPage),
        ...getBaseRequestParam(),
        source: SHOP_QUERY_SOURCE_ENUM.BASIC_SHOP_LIST,
      };

      const res = await getShopList(listParam);

      const { dataList, pageInfo } = res || {};
      const { totalCount } = pageInfo || {};

      (dataList || []).forEach((item) => {
        sendEvent(
          item?.shopTaskStatus === TASK_STATUS.COMPLETED
            ? 'VIEW_TASK_DETAIL_BTN'
            : 'TO_DO_TASK_BTN',
          'EXP',
        );
        if (item?.showShopBusinessNews) {
          sendEvent('CREATE_BUSINESS_NEWS_BTN', 'EXP');
        }
      });

      return {
        list: dataList || [],
        total: totalCount || 0,
      };
    },
    {
      form,
      defaultPageSize,
      manual: true,
    },
  );

  const handleSearch = (params?: any) => {
    return fetchList(
      { current: 1, pageSize: tableProps.pagination.pageSize || defaultPageSize },
      params,
    );
  };

  const onChange = (pagination: any, filters: any, sorter: any) => {
    const _selectedLabels = filters.shopInformation || [];
    if (_selectedLabels?.length) {
      setSelectedLabels(_selectedLabels);
    } else {
      setSelectedLabels([]);
    }
    const orderMap = {
      ascend: SHOP_SORT_TYPE_STATUS.ASC,
      descend: SHOP_SORT_TYPE_STATUS.DESC,
    };
    const { current: pageNum, pageSize: size } = pagination;
    const { order, field } = sorter;
    let sortBy = '';
    const sortType = orderMap[order] || '';
    if (sortType) {
      const sortByMap = {
        shopQualityScore: SORT_BY_STATUS.QUANTITYSCORE,
        signEffectiveTime: SORT_BY_STATUS.SIGN_EFFECTIVE_TIME,
      };
      sortBy = sortByMap[field] || '';
    }
    fetchList(
      {
        current: pageNum,
        pageSize: size,
        sorter: { order, field },
      },
      {
        ...form.getFieldsValue(),
        shopLabelCondition: _selectedLabels || [],
      },
    );
    setSortData({ sortBy, sortType });
  };

  useImperativeHandle(
    ref,
    () => ({
      search: async (params?: any) => {
        if (params) {
          form.resetFields();
          setSelectedLabels([]);
          form.setFieldsValue(params);
          await handleSearch(params);
        } else {
          await handleSearch();
        }
      },
      form,
      openTaskDetailDrawer: (data: any) => {
        if (shopTableRef.current) {
          shopTableRef.current.openTaskDetailDrawer(data);
        }
      },
    }),
    [fetchList, form],
  );

  useEffect(() => {
    sendEvent('SHOP_LIST', 'EXP');
    if (!hasTask) {
      handleSearch();
    }
  }, []);

  const handleFormValuesChange = (changedValues: any) => {
    traceClick(PageSPMKey.首页, ModuleSPMKey['门店列表.筛选'], {
      filterType: Object.keys(changedValues).join(','),
    });
  };

  return (
    <div>
      <Form form={form} onValuesChange={handleFormValuesChange}>
        <ShopSearchForm
          onSearch={handleSearch}
          form={form}
          isShow={isExpanded}
          loading={tableProps.loading}
          onFormReset={() => {
            setSelectedLabels([]);
          }}
        />
        <BatchSubmitModal />
        <ShopTable
          ref={shopTableRef}
          list={tableProps.dataSource || []}
          listLoading={tableProps.loading}
          pagination={tableProps.pagination}
          onChange={onChange}
          fetchList={handleSearch}
          sortData={sortData}
          selectedRowKey={selectedRowKey}
          setSelectedRowKey={setSelectedRowKey}
          urlQuery={urlQuery}
          selectedLabels={selectedLabels}
        />
      </Form>
    </div>
  );
});

Shop.displayName = 'Shop';

export default Shop;
