import React, { useEffect, useRef, useState } from 'react';
import { Card, Typography, Spin } from 'antd';
import styled from 'styled-components';
import * as echarts from 'echarts';
import { useRequest } from 'ahooks';
import { queryTodayMerchantTodoTaskPieChart } from '@/services';

const { Text } = Typography;

const TaskCard = styled(Card)`
  background: #f4f8ff;
  border-radius: 2px;
  border: none;

  .ant-card-body {
    padding: 0;
  }
`;

const CardTitle = styled.div`
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
  backdrop-filter: blur(10px);
  width: 138px;
  height: 56px;
  text-align: center;
  padding: 8px 16px;
  background-image: url('https://img.alicdn.com/imgextra/i1/O1CN01K5K5cM1WmBxH3kRsl_!!6000000002830-55-tps-138-56.svg');
  background-repeat: no-repeat;
  background-position: center right;
  background-size: auto 100%;
  margin-left: -4px;
`;

const CardTitleText = styled.div`
  justify-content: center;
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  display: flex;
  align-items: center;
  letter-spacing: normal;
  background: linear-gradient(90deg, #0078fe 0%, #00d1e8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;

  img {
    padding-right: 4px;
  }
`;

const ContentArea = styled.div`
  position: relative;
  z-index: 1;
  margin-top: -15px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.7) 100%);
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
  backdrop-filter: blur(10px);
  font-size: 14px;
  padding: 8px 28px 8px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  height: 170px;
`;

const ChartContainer = styled.div`
  width: 120px;
  height: 120px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const LegendContainer = styled.div`
  flex: 1;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
`;

const LegendItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
`;

const ColorDot = styled.div<{ color: string }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${(props) => props.color};
`;

const IntroText = styled.div`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #333;
  text-align: center;
  padding: 0 20px;
`;

export default function MerchantTasks() {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstanceRef = useRef<echarts.ECharts | null>(null);
  const [chartData, setChartData] = useState<
    Array<{ name: string; value: number; itemStyle: { color: string } }>
  >([]);
  const [strategySuggestion, setStrategySuggestion] = useState('暂无任务数据，请稍后再试');

  const colors = ['#0078fe', '#00d1e8', '#ffa940', '#722ed1', '#3491FA'];

  const { data: pieChartData, loading } = useRequest(async () => {
    const result = await queryTodayMerchantTodoTaskPieChart();
    return result;
  });

  useEffect(() => {
    if (pieChartData) {
      const { pieChartData: chartDataInfo, strategySuggestion: suggestion } = pieChartData;

      if (suggestion) {
        setStrategySuggestion(suggestion);
      }

      if (chartDataInfo?.layerDistribution) {
        const { totalMerchants } = chartDataInfo;
        const processedData = chartDataInfo.layerDistribution.map((item, index) => ({
          name: item.layerName,
          value:
            totalMerchants > 0
              ? Number(((item.merchantCount / totalMerchants) * 100).toFixed(1))
              : 0,
          itemStyle: { color: colors[index % colors.length] },
          merchantCount: item.merchantCount,
          taskCount: item.taskCount,
        }));

        setChartData(processedData);
      }
    }
  }, [pieChartData]);

  // 初始化图表
  useEffect(() => {
    if (chartRef.current && !chartInstanceRef.current) {
      const chart = echarts.init(chartRef.current);
      chartInstanceRef.current = chart;
    }

    return () => {
      if (chartInstanceRef.current) {
        chartInstanceRef.current.dispose();
        chartInstanceRef.current = null;
      }
    };
  }, []);

  // 更新图表数据
  useEffect(() => {
    if (chartInstanceRef.current && chartData.length > 0) {
      const option = {
        series: [
          {
            type: 'pie',
            radius: '70%',
            center: ['50%', '50%'],
            data: chartData,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
              },
            },
          },
        ],
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            const { data } = params;
            return `${data.name}: ${data.value}%<br/>商户数: ${data.merchantCount}<br/>任务数: ${data.taskCount}`;
          },
        },
      };

      chartInstanceRef.current.setOption(option);
    }
  }, [chartData]);

  return (
    <TaskCard>
      <CardTitle>
        <CardTitleText>商家任务</CardTitleText>
      </CardTitle>

      <ContentArea>
        {strategySuggestion === '暂无任务数据，请稍后再试' ? (
          <div style={{ textAlign: 'center', color: 'rgb(153, 153, 153)' }}>
            {strategySuggestion}
          </div>
        ) : (
          <>
            <IntroText>{strategySuggestion}</IntroText>
            <ChartContainer>
              <Spin spinning={loading} size="small">
                <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
              </Spin>
            </ChartContainer>

            <LegendContainer>
              {chartData.map((item) => (
                <LegendItem key={item.name}>
                  <ColorDot color={item.itemStyle.color} />
                  <Text style={{ fontSize: '12px' }}>
                    {item.name}: {item.value}%
                  </Text>
                </LegendItem>
              ))}
            </LegendContainer>
          </>
        )}
      </ContentArea>
    </TaskCard>
  );
}
