import React from 'react';
import { Flex, Card } from 'antd';
import styled from 'styled-components';
import DiagnosticSuggestion from '../diagnostic-suggestion';
import MerchantTasks from '../merchant-tasks';

const DashboardContainer = styled(Card)`
  background: #f4f8ff;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

export default function DashboardCards() {
  return (
    <DashboardContainer>
      <Flex gap={16} style={{ width: '100%', background: '#f4f8ff' }}>
        <div style={{ flex: 1 }}>
          <DiagnosticSuggestion />
        </div>
        <div style={{ flex: 1 }}>
          <MerchantTasks />
        </div>
      </Flex>
    </DashboardContainer>
  );
}
