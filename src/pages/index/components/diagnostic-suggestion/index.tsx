import React, { useEffect, useState } from 'react';
import { Card, Spin } from 'antd';
import styled from 'styled-components';
import { useRequest } from 'ahooks';
import { generateOperationDiagnosisSuggestion } from '@/services';

const SuggestionCard = styled(Card)`
  background: #f4f8ff;
  border-radius: 2px;
  border: none;

  .ant-card-body {
    padding: 0;
  }
`;

const CardTitle = styled.div`
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
  backdrop-filter: blur(10px);
  width: 176px;
  height: 56px;
  text-align: center;
  padding: 8px 16px;
  background-image: url('https://img.alicdn.com/imgextra/i3/O1CN01fLFkoB1bqNeqgMHAq_!!6000000003516-55-tps-176-56.svg');
  background-repeat: no-repeat;
  background-position: center right;
  background-size: auto 100%;
  margin-left: -4px;
`;

const CardTitleText = styled.div`
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  display: flex;
  align-items: center;
  letter-spacing: normal;
  background: linear-gradient(90deg, #0078fe 0%, #00d1e8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;

  img {
    padding-right: 4px;
  }
`;

const ContentArea = styled.div`
  position: relative;
  z-index: 1;
  margin-top: -15px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.7) 100%);
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
  backdrop-filter: blur(10px);
  font-size: 14px;
  padding: 16px 20px 16px 16px;
  line-height: 1.8;
  height: 170px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
`;

const DateRange = styled.div`
  color: #666;
  font-size: 12px;
  margin-bottom: 8px;
`;

const Paragraph = styled.div`
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const DateLabel = styled.div`
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  text-align: right;
  letter-spacing: normal;
  color: rgba(0, 0, 0, 0.45);
`;

const TitleContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export default function DiagnosticSuggestion() {
  const [diagnosisSuggestion, setDiagnosisSuggestion] = useState('暂无诊断建议，请稍后再试');
  const [generationTime, setGenerationTime] = useState('');

  const { data: suggestionData, loading } = useRequest(async () => {
    const result = await generateOperationDiagnosisSuggestion();
    return result;
  });

  useEffect(() => {
    if (suggestionData) {
      const { diagnosisSuggestion: suggestion, generationTime: time } = suggestionData;

      if (suggestion) {
        setDiagnosisSuggestion(suggestion);
      }

      if (time) {
        setGenerationTime(time);
      }
    }
  }, [suggestionData]);

  const paragraphs = diagnosisSuggestion.split('\n').filter((paragraph) => paragraph.trim());

  return (
    <SuggestionCard>
      <TitleContainer>
        <CardTitle>
          <CardTitleText>
            <img
              src="https://img.alicdn.com/imgextra/i4/O1CN01ieqykX1z2VIrXmCh4_!!6000000006656-55-tps-23-24.svg"
              alt=""
            />
            运维诊断建议
          </CardTitleText>
        </CardTitle>
        <DateLabel>{generationTime}</DateLabel>
      </TitleContainer>

      <ContentArea>
        <Spin spinning={loading}>
          {diagnosisSuggestion === '暂无诊断建议，请稍后再试' ? (
            <div style={{ color: 'rgb(153, 153, 153)' }}>{diagnosisSuggestion}</div>
          ) : (
            <div>
              <DateRange>{generationTime}</DateRange>
              {paragraphs.map((paragraph) => (
                <Paragraph key={`paragraph-${paragraph.slice(0, 20)}`}>{paragraph}</Paragraph>
              ))}
            </div>
          )}
        </Spin>
      </ContentArea>
    </SuggestionCard>
  );
}
