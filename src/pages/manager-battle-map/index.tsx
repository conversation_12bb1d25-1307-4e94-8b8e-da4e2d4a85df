import React, { useEffect } from 'react';
import { Flex } from 'antd';
import StickyBox from 'react-sticky-box';
import StoreDashboard from './components/StoreDashboard';
import MerchantStratification from './components/MerchantStratification';
import TaskDetails from './components/TaskDetails';
import FilterBar from './components/FilterBar';
import Performance from './components/Performance';
import { FilterProvider, useFilterOptions } from '@/context/FilterContext';

const ManagerBattleMapContent: React.FC = () => {
  const { filterOptions, setFilterOptions } = useFilterOptions();

  useEffect(() => {
    console.log('filterOptions Change', filterOptions);
  }, [filterOptions]);

  return (
    <>
      {/* 吸顶筛选条件栏 - 位于最前面 */}
      <StickyBox offsetTop={0} offsetBottom={20} style={{ zIndex: 1000 }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '16px 0 16px 16px',
            background: '#F5F5F5',
          }}
        >
          <div style={{ fontSize: 16, fontWeight: 500 }}>管理者作战地图</div>
          <FilterBar onChange={setFilterOptions} />
        </div>
      </StickyBox>
      <Flex gap={16} style={{ paddingLeft: 16, paddingRight: 16, background: '#F5F5F5' }} vertical>
        {/* 绩效 */}
        <Performance />

        {/* 门店仪表盘 */}
        <StoreDashboard />

        {/* 商户分层 */}
        <MerchantStratification />

        {/* 任务明细 */}
        <TaskDetails />
      </Flex>
    </>
  );
};

function ManagerBattleMap() {
  return (
    <FilterProvider>
      <ManagerBattleMapContent />
    </FilterProvider>
  );
}

export default ManagerBattleMap;
