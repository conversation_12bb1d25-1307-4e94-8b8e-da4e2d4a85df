export const CONTRAST_PERIOD_OPTIONS = [
  { value: 'DAY_ON_DAY', label: '日环比' },
  { value: 'WEEK_ON_WEEK  ', label: '周环比' },
  { value: 'MONTH_ON_MONTH', label: '月环比' },
];

// 阶段枚举
export const CYCLE_LIST = [
  { key: 'newbie', href: '#newbie', title: '新手期' },
  { key: 'bottleneck', href: '#bottleneck', title: '瓶颈期' },
  { key: 'develop', href: '#develop', title: '成长期' },
  { key: 'mature', href: '#mature', title: '成熟期' },
];

export const SUGGESTION_TEXT =
  '<span style="color: rgba(0, 0, 0, 0.85); font-weight: 500;">· 建议:</span>当前阶段目标均符合预期，<span style="color: rgba(0, 0, 0, 0.85); font-weight: 500;">请继续保持！</span>';

// 停留时长选项
export const STAY_DURATION_OPTIONS = [
  {
    label: '停留0~3天',
    value: 'LESS_THAN_THREE',
  },
  {
    label: '停留4~14天',
    value: 'FOUR_TO_FOURTEEN',
  },
  {
    label: '停留14天以上',
    value: 'MORE_THAN_FOURTEEN',
  },
];

// 排序枚举
export const SORT_ORDER_OPTIONS = [
  { value: 'ASC', label: '正序' },
  { value: 'DESC', label: '倒序' },
];

// 查询数据类型枚举值
export const NEWBIE_DATA_TYPE_OPTIONS = [
  { value: 'NEWBIE_SHOP', label: '未出新手期门店停留时长门店数' },
  { value: 'FIVE_STAR_SHELF_SHOP', label: '有五星装修任务的门店数' },
  { value: 'SHELF_HAS_PRODUCTS_SHOP', label: '有货架无品任务的门店数' },
];

// 瓶颈期查询数据类型枚举（成长期查询数据类型枚举）
export const DEVELOP_DATA_TYPE_OPTIONS = [{ value: 'NUM_OF_SHOP', label: '门店数' }];

// 成熟期查询数据类型枚举
export const MATURE_DATA_TYPE_OPTIONS = [
  { value: 'NUM_OF_SHOP', label: '门店数' },
  { value: 'ARPU_WEEK_GROWTH', label: '日均店均ARPU周增幅' },
];
