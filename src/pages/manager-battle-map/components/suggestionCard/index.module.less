.detail_card {
  background: #f4f8ff;
  border-radius: 2px;

  .card_title {
    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
    backdrop-filter: blur(10px);
    width: 138px;
    height: 56px;
    text-align: center;
    padding: 8px 16px;
    background-image: url('https://img.alicdn.com/imgextra/i1/O1CN01K5K5cM1WmBxH3kRsl_!!6000000002830-55-tps-138-56.svg');
    background-repeat: no-repeat;
    background-position: center right; /* 背景图靠右居中 */
    background-size: auto 100%; /* 高度占满，宽度自适应 */
    margin-left: -4px;

    .card_title_text {
      font-family: PingFang SC;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      display: flex;
      align-items: center;
      letter-spacing: normal;
      background: linear-gradient(90deg, #0078fe 0%, #00d1e8 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
      img {
        padding-right: 4px;
      }
    }
  }

  .card_content {
    position: relative;
    z-index: 1;
    margin-top: -15px;
  }

  .card_store {
    background: rgba(255, 255, 255, 0.4);
    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
    backdrop-filter: blur(10px);
    font-size: 14px;
    padding: 8px 28px 6px 16px;
    :global {
      .ant-typography {
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }

  :global {
    .uiw-markdown-preview {
      position: relative;
      z-index: 1;
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 255, 255, 0.7) 100%
      );
      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
      backdrop-filter: blur(10px);
      font-size: 14px;
      padding: 8px 28px 8px 16px;
    }
  }
}
