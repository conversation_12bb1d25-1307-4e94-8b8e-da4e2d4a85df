.storeCycle {
  padding: 16px 0;
}

.extraText {
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  text-align: right;
  letter-spacing: normal;
  color: rgba(0, 0, 0, 0.45);
  margin-left: 16px;
}

.cycleCardTitle {
  display: flex;
  align-items: center;

  span {
    padding-right: 4px;
  }
}

.statisticCardSuffix {
  font-size: 12px;
}

.comparison {
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: normal;
  color: rgba(0, 0, 0, 0.45);
}

.declineCard {
  :global {
    .ant-pro-card-body {
      padding: 0;
    }
  }
}

.cycleCard {
  :global {
    .ant-pro-statistic-card-chart {
      margin: 0;
    }
  }

  .chartTitle {
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: normal;
    color: rgba(0, 0, 0, 0.45);
  }
}
