/**
 * 门店仪表盘
 */

import { Card, Select, Form } from 'antd';
import React, { useEffect } from 'react';
import { CONTRAST_PERIOD_OPTIONS } from '@/pages/manager-battle-map/constant';
import { queryMerchantShopDashboard } from '@/services/manager-battle-map';
import SuggestionCard from '../suggestionCard';
import StoreCycle from './components/storeCycle';

import styles from './index.module.less';

const StoreDashboard = () => {
  const [form] = Form.useForm();

  const getDashboardData = async () => {
    const params = form.getFieldsValue();
    console.log('params: ', params);
    try {
      const res = await queryMerchantShopDashboard({ params });
      console.log(res);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getDashboardData();
  }, []);

  // 环比
  const onValuesChange = () => {
    getDashboardData();
  };

  const talkContent =
    '你好，我是高德地图负责咱们<span style="color: red;">金华市</span>这一块官方运营经理<span style="color: red;">龙斌(wb-lb701683)</span>。\n\n请问是<span style="color: red;">东晟图文</span>的负责人吗？';
  const extraRender = () => {
    return (
      <Form onValuesChange={onValuesChange} form={form} layout="inline">
        <Form.Item name="cycleRatioType" label="" initialValue="DAY_ON_DAY">
          <Select style={{ width: 120 }} options={CONTRAST_PERIOD_OPTIONS} />
        </Form.Item>
        <Form.Item>
          <span className={styles.extraText}>数据来源于昨天</span>
        </Form.Item>
      </Form>
    );
  };

  return (
    <Card title="门店仪表盘" extra={extraRender()} bodyStyle={{ padding: '16px 24px' }}>
      {/* 诊断建议 */}
      <SuggestionCard suggest={talkContent} />
      {/* 门店周期 */}
      <StoreCycle />
    </Card>
  );
};

export default StoreDashboard;
