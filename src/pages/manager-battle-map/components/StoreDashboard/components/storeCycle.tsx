import React from 'react';
import CycleCard from './cycleCard';
import { Col, Row } from 'antd';
import { CARD_CONFIGS } from '../constant';

import styles from '../index.module.less';

const StoreCycle = ({ onClick = () => {} }) => {
  const chartsData = [
    { label: '14天以上', value: 0.6, desc: '14天以上' },
    { label: '4-14天', value: 0.45, desc: '4-14天' },
    { label: '0-3天', value: 0.3, desc: '0-3天' },
  ];
  return (
    <Row gutter={36} className={styles.storeCycle}>
      {CARD_CONFIGS.map((config) => (
        <Col span={6} key={config.phase}>
          <CycleCard
            onClick={onClick}
            chartsData={chartsData}
            cardTitle={config.cardTitle}
            cardDesc={config.cardDesc}
            chartTitle={config.chartTitle}
            chartDesc={config.chartDesc}
          />
        </Col>
      ))}
    </Row>
  );
};

export default StoreCycle;
