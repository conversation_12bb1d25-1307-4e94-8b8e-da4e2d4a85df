import React from 'react';
import { Tooltip, Space, Progress, Divider } from 'antd';
import { QuestionCircleOutlined, RiseOutlined, FallOutlined } from '@ant-design/icons';
import { StatisticCard } from '@ant-design/pro-components';
import BarCharts from './barCharts';

import styles from '../index.module.less';

const { Statistic } = StatisticCard;

interface CycleCardProps {
  chartsData: any[]; // 图表数据
  cardTitle: string; // 卡片标题
  cardDesc: string; // 卡片描述
  chartTitle: string; // 图表标题
  chartDesc: string; // 图表描述
}
const CycleCard = (props: CycleCardProps) => {
  const { chartsData, cardTitle, cardDesc, chartTitle, chartDesc, onClick = () => {} } = props;
  // 上升
  const riseStyle = { color: '#F5222D', lineHeight: '20px' };
  const riseIcon = <RiseOutlined style={{ color: '#F5222D' }} />;
  // 下降
  const declineStyle = { color: '#52C41A' };
  const declineIcon = <FallOutlined style={{ color: '#52C41A', lineHeight: '20px' }} />;

  const titleRender = ({ title = '', desc = '' }) => {
    return (
      <div className={styles.cycleCardTitle}>
        <span>{title}</span>
        <Tooltip title={desc}>
          <QuestionCircleOutlined />
        </Tooltip>
      </div>
    );
  };

  const descriptionRender = () => {
    return (
      <>
        <Progress percent={50} showInfo={false} />
        <Statistic
          title={<span className={styles.comparison}>{'日环比'}</span>}
          prefix={riseIcon}
          value="82.3%"
          valueStyle={riseStyle}
        />
        <Divider style={{ margin: '8px 0' }} />
        <div className={styles.declineCard}>
          <StatisticCard
            statistic={{
              value: 1193,
              suffix: <span className={styles.statisticCardSuffix}>个</span>,
              description: (
                <Statistic
                  title={<span className={styles.comparison}>{'日环比'}</span>}
                  prefix={declineIcon}
                  value="82.3%"
                  valueStyle={declineStyle}
                />
              ),
            }}
          />
        </div>
        <Divider style={{ margin: '8px 0' }} />
      </>
    );
  };

  return (
    <div className={styles.cycleCard}>
      <StatisticCard
        hoverable
        onClick={onClick}
        bordered
        title={<Space>{titleRender({ title: cardTitle, desc: cardDesc })}</Space>}
        statistic={{
          value: 1103,
          suffix: <span className={styles.statisticCardSuffix}>%</span>,
          description: descriptionRender(),
        }}
        chart={
          <>
            <div className={styles.chartTitle}>
              {titleRender({ title: chartTitle, desc: chartDesc })}
            </div>
            <BarCharts
              data={chartsData}
              xField="label"
              yField="value"
              height={200}
              customConfig={{
                legend: false, // 隐藏图例
                style: {
                  maxWidth: 20, // 柱子宽度
                },
                scale: {
                  y: {
                    // domainMax: 1, //  坐标终点
                    domainMin: 0, // 坐标起点
                    tickCount: 4, //  柱子数量
                  },
                  color: {
                    type: 'ordinal',
                    domain: ['14天以上', '4-14天', '0-3天'], // 明确分类顺序
                    range: ['#F53F3F', '#FAAD14', '#52C41A'],
                  },
                },
                sort: false,
                // ✅ 2. 设置颜色
                colorField: 'desc',
                label: {
                  formatter: (datum) => {
                    // 确保 frequency 是数字
                    const value = parseFloat(datum);
                    return isNaN(value) ? '' : `${(value * 100).toFixed(1)}%`;
                  },
                },
                axis: {
                  y: {
                    grid: true,
                    gridStrokeOpacity: 0.5,
                    labelFormatter: (val) => {
                      const num = parseFloat(val);
                      return isNaN(num) ? '' : `${(num * 100).toFixed(0)}%`;
                    },
                  },
                },
                tooltip: (d) => {
                  return {
                    name: d?.letter,
                    value: `${(d?.frequency * 100).toFixed(1)}%`,
                  };
                },
              }}
            />
          </>
        }
        style={{ minWidth: 257 }}
        headStyle={{ padding: '8px 8px 0' }}
        bodyStyle={{ padding: 8 }}
      />
    </div>
  );
};

export default CycleCard;
