import React, { useCallback, useMemo, useState } from 'react';
import { Form, Button, Radio, Card, message } from 'antd';
import { ProTable } from '@ant-design/pro-components';

import styles from './index.module.less';
import { queryOptTodoTask } from '@/services/manager-battle-map';

const TaskExpediting = () => {
  const [form] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [submitting, setSubmitting] = useState(false);

  const columns = [
    { title: '商户名称', dataIndex: 'merchantName' },
    { title: '未达标门店数量', dataIndex: 'shopCount' },
    { title: '任务总量', dataIndex: 'totalTaskCount' },
  ];

  const dataSource = [
    { merchantName: '1', shopCount: 5, totalTaskCount: 10, merchantId: 1 },
    { merchantName: '2', shopCount: 3, totalTaskCount: 8, merchantId: 2 },
  ];

  const taskOptions = [
    { label: '货架精品', value: 'option1' },
    { label: '五星装修', value: 'option2' },
  ];

  const onRowSelectionChange = useCallback(
    (keys, rows) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
      form.setFieldsValue({ merchantIdList: rows });
    },
    [form],
  );

  const rowSelection = useMemo(
    () => ({
      selectedRowKeys,
      onChange: onRowSelectionChange,
    }),
    [selectedRowKeys, onRowSelectionChange],
  );

  // 提交
  const handleSubmit = useCallback(() => {
    form
      .validateFields()
      .then(async (values) => {
        console.log('values: ', values);
        setSubmitting(true);
        const payload = {
          taskType: values.taskType,
          merchantIdList: values.merchantIdList || selectedRows,
        };
        console.log('提交内容：', payload);
        // try {
        //   await queryOptTodoTask({ params: payload });
        //   message.success('提交成功');
        // } catch (err) {
        //   console.log('请求失败：', err);
        // }
      })
      .catch((err) => {
        console.log('校验失败：', err);
      });
  }, [form, selectedRows]);

  return (
    <Card
      title="任务催办"
      bodyStyle={{ paddingBottom: 0 }}
      className={styles.actions_card}
      actions={[
        <div key="actions" style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
          <Button type="primary" onClick={handleSubmit} loading={submitting}>
            确认催办
          </Button>
        </div>,
      ]}
    >
      <Form form={form} initialValues={{ taskType: undefined, merchantIdList: [] }}>
        <Form.Item
          name="taskType"
          label="任务名称"
          rules={[{ required: true, message: '请选择一个任务' }]}
        >
          <Radio.Group options={taskOptions} />
        </Form.Item>

        <Form.Item name="merchantIdList" rules={[{ required: true, message: '请选择一个商户' }]}>
          <ProTable
            rowKey="merchantId"
            columns={columns}
            dataSource={dataSource}
            search={false}
            pagination={false}
            options={false}
            rowSelection={rowSelection}
            request={async (params) => {
              try {
                //   const res = await queryOptTodoTask({ params });
                console.log('请求参数：', params);
              } catch (err) {
                console.log('请求失败：', err);
              }
              return {
                data: dataSource,
                success: true,
              };
            }}
          />
        </Form.Item>
      </Form>
    </Card>
  );
};

export default TaskExpediting;
