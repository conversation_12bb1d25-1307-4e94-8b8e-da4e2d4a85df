import { Card, Empty } from 'antd';
import React, { useEffect, useState } from 'react';
import { queryEmployeeAnalysis } from '@/services/manager-battle-map';
import Overview from './overview';

import styles from './index.module.less';

const MerchantAnalysis = () => {
  const [employeeAnalysis, setEmployeeAnalysis] = useState<any>({}); // 小二分析

  // 获取小二分析
  const getEmployeeAnalysis = async () => {
    try {
      const res = await queryEmployeeAnalysis();
      setEmployeeAnalysis(res?.data);
      console.log('res', res);
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    getEmployeeAnalysis();
  }, []);

  const data = {
    newbieDiagnose: '新手期诊断建议',
    bottleneckDiagnose: '瓶颈期诊断建议',
    growthDiagnose: '成长期诊断建议',
    matureDiagnose: '完成期诊断建议',
  };

  const dataList = [
    { layerName: '新手期', layerCode: 'NEWBIE', shopCount: 3, taskCount: 8 },
    { layerName: '成长期', layerCode: 'GROWTH', shopCount: 3, taskCount: 7 },
    { layerName: '瓶颈期', layerCode: 'BOTTLENECK', shopCount: 2, taskCount: 6 },
    { layerName: '成熟期', layerCode: 'MATURE', shopCount: 1, taskCount: 3 },
  ];

  const suggestionList = (data: any) => {
    if (data) {
      return (Object.keys(data) || [])?.map((key) => ({
        key,
        value: data[key],
      }));
    }
  };
  return (
    <>
      <Card className={styles.detail_card} bodyStyle={{ padding: '16px 20px' }}>
        <div className={styles.card_title}>
          <div className={styles.card_title_text}>
            <img
              src="https://img.alicdn.com/imgextra/i4/O1CN01ieqykX1z2VIrXmCh4_!!6000000006656-55-tps-23-24.svg"
              alt=""
            />
            小二分析
          </div>
          <span className={styles.card_title_extra}>平台为您推荐以下数据，辅助您完成运维任务</span>
        </div>
        {dataList && dataList.length > 0 ? (
          <Overview
            title="门店概览"
            extra="当前商户共有24个任务需要处理，其中3个任务被主管催办需要重点关注"
            dataList={dataList}
          />
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
        <Overview
          title="诊断建议"
          extra="在2025年02月19日 到2025年02月25日 期间，您的店铺经营情况如下："
          data={suggestionList(data)}
        />
      </Card>
    </>
  );
};

export default MerchantAnalysis;
