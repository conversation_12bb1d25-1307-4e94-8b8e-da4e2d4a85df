.detail_card {
  background: #f4f8ff;
  border-radius: 2px;
  margin-bottom: 16px;

  .card_title {
    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;

    .card_title_text {
      font-family: PingFang SC;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      display: flex;
      align-items: center;
      letter-spacing: normal;
      background: linear-gradient(90deg, #0078fe 0%, #00d1e8 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      color: transparent;
      text-fill-color: transparent;
      img {
        padding-right: 4px;
      }
    }
    .card_title_extra {
      color: rgba(0, 0, 0, 0.65);
      font-weight: normal;
      letter-spacing: normal;
      font-size: 14px;
      padding-left: 8px;
    }
  }
  :global {
    .uiw-markdown-preview {
      position: relative;
      z-index: 1;
      margin-top: -15px;
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 255, 255, 0.7) 100%
      );
      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
      backdrop-filter: blur(10px);
      font-size: 14px;
      padding: 8px 28px 8px 16px;
    }
  }
}

.drawer_card_header {
  background: rgba(255, 255, 255, 0.6);
  padding: 0 16px;
  margin-top: 10px;

  .drawer_card_title {
    display: flex;
    align-items: center;
    font-size: 14px;

    span {
      font-weight: normal;
      color: rgba(0, 0, 0, 0.65);
      padding-left: 8px;
      font-size: 12px;
    }
  }
}

.actions_card {
  :global {
    .ant-card-actions {
      border-top: none;
      padding: 12px 24px;
    }
  }
}

.layer_name {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.85);
}

.layer_item {
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.65);
}
