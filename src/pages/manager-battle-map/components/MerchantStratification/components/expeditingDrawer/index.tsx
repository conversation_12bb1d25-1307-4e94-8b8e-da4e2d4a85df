/**
 * 催办抽屉
 */

import { Drawer } from 'antd';
import React from 'react';
import MerchantAnalysis from './merchantAnalysis';
import TaskExpediting from './taskExpediting';

interface ExpeditingDrawerProps {
  open: boolean;
  onClose: (open: boolean) => void;
}
const ExpeditingDrawer = (props: ExpeditingDrawerProps) => {
  const { open, onClose } = props;

  return (
    <Drawer
      title="服务商诊断详情"
      onClose={onClose}
      open={open}
      closable
      destroyOnHidden
      width={930}
      bodyStyle={{ background: '#FAFAFA', padding: '16px 24px' }}
    >
      <MerchantAnalysis />
      <TaskExpediting />
    </Drawer>
  );
};

export default ExpeditingDrawer;
