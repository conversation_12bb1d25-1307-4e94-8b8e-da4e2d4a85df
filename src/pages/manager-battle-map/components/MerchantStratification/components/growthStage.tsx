/**
 * 成长期数据诊断
 */

import { Card, Form, Select, Space } from 'antd';
import React, { useState } from 'react';
import SuggestionCard from '../../suggestionCard';
import Expediting from './expediting';
import { DEVELOP_DATA_TYPE_OPTIONS, SORT_ORDER_OPTIONS } from '@/pages/manager-battle-map/constant';
import ColumnCharts from './columnCharts';

import styles from '../index.module.less';

const GrowthStage = () => {
  const [form] = Form.useForm();

  // 筛选
  const onValuesChange = (values: any) => {
    console.log('CompareChange', values);
  };
  const talkContent =
    '你好，我是高德地图负责咱们<span style="color: red;">金华市</span>这一块官方运营经理<span style="color: red;">龙斌(wb-lb701683)</span>。\n\n请问是<span style="color: red;">东晟图文</span>的负责人吗？';

  // 催办任务列表（可来自接口）
  const expeditingTasks = [
    {
      id: 1,
      suggest: 'XXX小二X个门店12天以上未完成',
      remindFlag: true,
      shopCount: 12,
    },
    {
      id: 2,
      suggest: 'XXX小二X个门店31天以上未完成',
      remindFlag: false,
      shopCount: 8,
    },
    {
      id: 3,
      suggest: 'XXX小二X个门店312天以上未完成',
      remindFlag: true,
      shopCount: 15,
    },
  ];
  const storeDataList = {
    numOfShop: 31212,
    ratio: '80%',
    MonthOnMonth: '80%',
    weekOnWeek: '30%',
  };
  const extraRender = () => {
    return (
      <Form onValuesChange={onValuesChange} form={form} layout="inline">
        <Form.Item name="dataType" label="" initialValue="NUM_OF_SHOP">
          <Select style={{ width: 120 }} options={DEVELOP_DATA_TYPE_OPTIONS} />
        </Form.Item>
        <Form.Item name="sortType" label="" initialValue="DESC">
          <Select style={{ width: 120 }} options={SORT_ORDER_OPTIONS} />
        </Form.Item>
      </Form>
    );
  };

  const data = [
    {
      state: 'AL',
      age: '<10',
      population: 5984,
    },
    {
      state: 'AK',
      age: '<10',
      population: 2063,
    },
    {
      state: 'AZ',
      age: '<10',
      population: 1921,
    },
    {
      state: 'AL',
      age: '10-19',
      population: 3921,
    },
    {
      state: 'AK',
      age: '10-19',
      population: 5038,
    },
    {
      state: 'AZ',
      age: '10-19',
      population: 6908,
    },
    {
      state: 'AL',
      age: '20-29',
      population: 3993,
    },
    {
      state: 'AK',
      age: '20-29',
      population: 2121,
    },
    {
      state: 'AZ',
      age: '20-29',
      population: 2437,
    },
  ];
  return (
    <Card
      title="成长期数据诊断"
      variant="borderless"
      extra={<span className={styles.extraText}>数据来源于昨天</span>}
      bodyStyle={{ padding: '16px 24px' }}
    >
      {/* 诊断建议 */}
      <SuggestionCard
        suggest={talkContent}
        remindList={expeditingTasks}
        storeDataList={storeDataList}
      />
      <Card
        title="日均凭证量分布"
        variant="borderless"
        extra={extraRender()}
        bodyStyle={{ padding: '24px 24px 0 24px' }}
        className={styles.no_shadow_card}
      >
        {/* 图表 */}
        <ColumnCharts
          data={data}
          xField="state"
          yField="population"
          height={255}
          customConfig={{
            stack: true,
            colorField: 'age',
          }}
        />
      </Card>
    </Card>
  );
};

export default GrowthStage;
