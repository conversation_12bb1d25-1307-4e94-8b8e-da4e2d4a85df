/**
 * 新手期数据诊断
 */

import { Card, Form, Select, Space } from 'antd';
import React, { useState } from 'react';
import SuggestionCard from '../../suggestionCard';
import Expediting from './expediting';
import {
  NEWBIE_DATA_TYPE_OPTIONS,
  STAY_DURATION_OPTIONS,
} from '@/pages/manager-battle-map/constant';
import ColumnCharts from './columnCharts';

import styles from '../index.module.less';

const NewStage = () => {
  const [form] = Form.useForm();

  // 筛选
  const onValuesChange = (values: any) => {
    console.log('CompareChange', values);
  };
  const talkContent =
    '你好，我是高德地图负责咱们<span style="color: red;">金华市</span>这一块官方运营经理<span style="color: red;">龙斌(wb-lb701683)</span>。\n\n请问是<span style="color: red;">东晟图文</span>的负责人吗？';

  // 催办任务列表（可来自接口）
  const expeditingTasks = [
    {
      id: 1,
      suggest: 'XXX小二X个门店12天以上未完成',
      remindFlag: true,
      shopCount: 12,
    },
    {
      id: 2,
      suggest: 'XXX小二X个门店31天以上未完成',
      remindFlag: false,
      shopCount: 8,
    },
    {
      id: 3,
      suggest: 'XXX小二X个门店312天以上未完成',
      remindFlag: true,
      shopCount: 15,
    },
  ];
  const storeDataList = {
    numOfShop: 31212,
    ratio: '80%',
    MonthOnMonth: '80%',
    weekOnWeek: '30%',
  };

  const extraRender = () => {
    return (
      <Form onValuesChange={onValuesChange} form={form} layout="inline">
        <Form.Item name="dataType" label="" initialValue="NEWBIE_SHOP">
          <Select style={{ width: 240 }} options={NEWBIE_DATA_TYPE_OPTIONS} />
        </Form.Item>
        <Form.Item name="stayTimeRange" label="" initialValue="LESS_THAN_THREE">
          <Select style={{ width: 120 }} options={STAY_DURATION_OPTIONS} />
        </Form.Item>
      </Form>
    );
  };

  const data = [
    { type: '1-3秒', value: 0.36 },
    { type: '4-10秒', value: 0.25 },
    { type: '11-30秒', value: 0.24 },
    { type: '31-60秒', value: 0.19 },
    { type: '1-3分', value: 0.12 },
    { type: '3-10分', value: 0.15 },
    { type: '10-30分', value: 0.16 },
    { type: '30+分', value: 0.1 },
  ];

  return (
    <Card
      title="新手期数据诊断"
      variant="borderless"
      extra={<span className={styles.extraText}>数据来源于昨天</span>}
      bodyStyle={{ padding: '16px 24px' }}
    >
      {/* 诊断建议 */}
      <SuggestionCard
        suggest={talkContent}
        remindList={expeditingTasks}
        storeDataList={storeDataList}
      />
      <Card
        title="指标&完成度"
        variant="borderless"
        extra={extraRender()}
        bodyStyle={{ padding: '24px 24px 0 24px' }}
        className={styles.no_shadow_card}
      >
        {/* 图表 */}
        <ColumnCharts
          data={data}
          xField="type"
          yField="value"
          height={255}
          labelFormatterY={(val) => `${(parseFloat(val) * 100).toFixed(0)}%`}
          customConfig={{
            label: {
              text: (d) => `${(d.value * 100).toFixed(1)}%`,
              textBaseline: 'bottom',
            },
          }}
        />
      </Card>
    </Card>
  );
};

export default NewStage;
