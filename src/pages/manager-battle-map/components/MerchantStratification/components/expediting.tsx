import React, { useState } from 'react';
import { Button, Space } from 'antd';
import { ArrowRightOutlined } from '@ant-design/icons';
import ExpeditingDrawer from './expeditingDrawer';

import styles from '../index.module.less';

interface ExpeditingProps {
  /** 催办文案 */
  suggest?: string;
  /** 是否展示催办按钮 */
  remindFlag: boolean;
}
const Expediting = (props: ExpeditingProps) => {
  const { suggest = '服务商诊断详情', remindFlag } = props;
  const [open, setOpen] = useState(false);

  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  return (
    <Space className={styles.expediting}>
      <img
        src="https://img.alicdn.com/imgextra/i2/O1CN01WBwlW91nukpPzBVpz_!!6000000005150-2-tps-40-40.png"
        alt=""
      />
      <div>{suggest}</div>
      {remindFlag && (
        <Button onClick={handleOpen} type="link" className={styles.expediting_btn}>
          去催办
          <ArrowRightOutlined />
        </Button>
      )}
      <ExpeditingDrawer open={open} onClose={handleClose} />
    </Space>
  );
};

export default Expediting;
