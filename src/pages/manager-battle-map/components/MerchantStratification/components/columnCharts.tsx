// ColumnCharts.tsx
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Column } from '@ant-design/plots';
import ExpeditingDrawer from './expeditingDrawer';

interface ColumnChartsProps {
  data: any[];
  xField: string;
  yField: string;
  height?: number;
  //   stack?: boolean; // 是否堆叠
  //   label?: any; // 标签(柱子上的数值展示)
  labelFormatterY?: (val: string) => string; // Y 轴标签格式化
  //   colorField?: string; // 柱子颜色(会显示图例)
  customConfig?: any; // 自定义配置，会覆盖默认配置
}

const ColumnCharts = (props: ColumnChartsProps) => {
  const { data, xField, yField, height = 255, labelFormatterY, customConfig = {} } = props;

  const chartRef = useRef(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [currentDatum, setCurrentDatum] = useState(''); // 当前点击的数据维度
  console.log('currentDatum: ', currentDatum);

  const labelFormatter = useCallback((datum, index) => {
    const { chart } = chartRef.current;
    if (!chart) return datum;

    const { document } = chart.getContext().canvas;
    const group = document.createElement('g', {});

    // 1. 添加原始文本
    const label = document.createElement('text', {
      style: {
        text: datum,
        fontSize: 12,
        fill: 'gray',
        textAlign: 'center',
        transform: `translate(0, 20)`,
      },
    });

    // ✅ 模拟 Ant Design 默认按钮样式
    const rect = document.createElement('rect', {
      style: {
        x: -24, // 宽度 48
        y: -12, // 高度 24
        width: 48,
        height: 24,
        radius: 2,
        fill: '#FFFFFF', // 主色
        stroke: 'rgba(0, 0, 0, 0.15)', // 边框
        lineWidth: 1,
        cursor: 'pointer',
        transform: `translate(0, 40)`,
      },
    });

    // ✅ 按钮文字：Ant Design 字体大小 + 白色
    const buttonText = document.createElement('text', {
      style: {
        text: '催办',
        fill: '#000',
        fontSize: 14,
        textAlign: 'center',
        textBaseline: 'middle',
        cursor: 'pointer',
        transform: `translate(0, 40)`,
      },
    });

    // ✅ 点击事件
    group.addEventListener('click', () => {
      setCurrentDatum(datum); // 记录当前点击的是哪个区间
      setDrawerOpen(true);
    });

    group.appendChild(rect);
    group.appendChild(buttonText);
    group.appendChild(label);

    return group;
  }, []);

  const config = useMemo(() => {
    return {
      data,
      height,
      xField,
      yField,
      axis: {
        x: {
          size: 60,
          labelFormatter,
        },
        y: {
          grid: true, // 网格线
          gridStrokeOpacity: 0.5, // 网格线透明度
          labelFormatter: labelFormatterY,
        },
      },
      // ✅ 添加缩略轴（slider）
      slider: {
        x: {},
      },
      // ✅ onReady：可以监听筛选事件
      onReady: (plot) => {
        chartRef.current = plot;
      },
      ...customConfig,
    };
  }, [data, labelFormatter]); // 依赖 data 和 labelFormatter

  return (
    <>
      <Column {...config} ref={chartRef} />
      {/* 全局抽屉 */}
      <ExpeditingDrawer open={drawerOpen} onClose={() => setDrawerOpen(false)} />
    </>
  );
};

export default ColumnCharts;
