.anchorContainer {
  @height-light-color: #1890ff;
  background-color: #f4f4f4;
  z-index: 200;
  padding-bottom: 16px;

  .anchor {
    height: 46px;
    display: flex;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 2px;
    position: relative;
  }
}

.extraText {
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  text-align: right;
  letter-spacing: normal;
  color: rgba(0, 0, 0, 0.45);
  margin-left: 16px;
}

.expediting {
  height: 36px;
  padding: 8px;
  margin-top: 8px;
  border-radius: 4px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.7) 100%);
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 100%) 1;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: normal;
  color: #3d3d3d;
  vertical-align: baseline;

  img {
    width: 20px;
    height: 20px;
    vertical-align: bottom;
  }

  .expediting_btn {
    margin: 0;
    padding: 0;
    font-size: 12px;
  }
}

.no_shadow_card {
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
}
