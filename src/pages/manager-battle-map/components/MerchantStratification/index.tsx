/**
 * 商家分层
 */

import { Anchor, Flex } from 'antd';
import React, { useState } from 'react';
import StickyBox from 'react-sticky-box';
import { CYCLE_LIST } from '../../constant';
import NewStage from './components/newStage';
import BarrierStage from './components/barrierStage';
import GrowthStage from './components/growthStage';
import MatureStage from './components/matureStage';

import styles from './index.module.less';

const MerchantStratification = () => {
  return (
    <div>
      <StickyBox className={styles.anchorContainer} offsetTop={0}>
        <Anchor
          className={styles.anchor}
          getContainer={() => document.getElementById('main') || window}
          getCurrentAnchor={(key) => {
            return key;
          }}
          replace
          targetOffset={200}
          direction="horizontal"
          items={CYCLE_LIST}
        />
      </StickyBox>
      <Flex gap={16} vertical>
        <div id="newbie">
          <NewStage />
        </div>
        <div id="bottleneck">
          <BarrierStage />
        </div>
        <div id="develop">
          <GrowthStage />
        </div>
        <div id="mature">
          <MatureStage />
        </div>
      </Flex>
    </div>
  );
};

export default MerchantStratification;
