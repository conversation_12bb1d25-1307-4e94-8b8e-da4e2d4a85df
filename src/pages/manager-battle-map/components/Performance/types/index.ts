/**
 * 绩效组件类型定义
 */

export interface PerformanceData {
  indicatorCode: string;
  indicatorName: string;
  indicatorValue: string;
  unit: string;
  targetValue: string;
  targetCompletionRate: string;
  monthOnMonthRate: string;
  weekOnWeekRate: string;
}

export interface PerformanceQueryParams {
  pageSource: string;
  positionId: string;
  staffId: string;
  companyId: string;
  queryDate: string;
  commonOperatorInfo: {
    operatorId: string;
    operatorName: string;
  };
}

export interface PerformanceResponse {
  result: boolean;
  code: string;
  message: string | null;
  version: string;
  timestamp: string;
  success: boolean;
  msgInfo: string;
  msgCode: string;
  traceId: string;
  data: {
    result: boolean;
    traceId: string;
    code: string;
    data: {
      performanceDataList: PerformanceData[];
    };
    success: boolean;
    message: string | null;
    msgInfo: string;
    version: string;
    msgCode: string;
    timestamp: string;
  };
}

export interface PerformanceMetricCardProps {
  data: PerformanceData;
  isMainCard?: boolean;
  isSelected?: boolean;
  onClick?: () => void;
}

export interface PerformanceChartProps {
  data: PerformanceData[];
  selectedIndicator?: string;
}

// 图表数据查询相关类型
export interface PerformanceChartQueryParams {
  pageSource: string;
  positionId: string;
  staffId: string;
  companyId: string;
  queryDate: string;
  comparisonPeriodType: string;
  pageInfo: {
    pageNo: number;
    pageSize: number;
  };
  jumpSource: string;
  selectedIndicator: string;
  commonOperatorInfo: {
    operatorId: string;
    operatorName: string;
  };
}

export interface BarChartData {
  teamCode: string;
  teamName: string;
  staffId: string;
  staffName: string;
  currentValue: string;
}

export interface PageInfo {
  totalCount: number;
  pageNo: number;
  pageSize: number;
  hasMore: boolean;
}

export interface PerformanceChartResponse {
  result: boolean;
  code: string;
  message: string | null;
  version: string;
  timestamp: string;
  success: boolean;
  msgInfo: string;
  msgCode: string;
  traceId: string;
  data: {
    result: boolean;
    traceId: string;
    code: string;
    data: {
      barChartDataList: BarChartData[];
      pageInfo: PageInfo;
      disclaimer: string;
    };
    success: boolean;
    message: string | null;
    msgInfo: string;
    version: string;
    msgCode: string;
    timestamp: string;
  };
}
