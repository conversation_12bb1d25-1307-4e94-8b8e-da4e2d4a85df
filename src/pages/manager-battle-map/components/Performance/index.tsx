/**
 * 绩效组件
 */

import React, { useEffect, useState } from 'react';
import { Card, Form, Select, Flex } from 'antd';
import PerformanceMetrics from './components/PerformanceMetrics';
import PerformanceChart from './components/PerformanceChart';
import { queryPerformanceTarget, mockPerformanceData } from './services/performanceService';
import { useFilterOptions } from '@/context/FilterContext';
import type { PerformanceData, PerformanceQueryParams } from './types';

import styles from './index.module.less';

const Performance: React.FC = () => {
  const { filterOptions } = useFilterOptions(); // 获取全局筛选条件
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
  const [selectedIndicator, setSelectedIndicator] = useState<string>('TOTAL_INCOME'); // 默认选中总收入

  // 获取绩效数据
  const fetchPerformanceData = async (params?: Partial<PerformanceQueryParams>) => {
    setLoading(true);
    try {
      const defaultParams: PerformanceQueryParams = {
        pageSource: 'MANAGER_PAGE',
        positionId: 'POS001',
        staffId: 'STAFF001',
        companyId: 'COMP001',
        queryDate: '20240115',
        commonOperatorInfo: {
          operatorId: 'OP001',
          operatorName: '张三',
        },
        ...params,
      };

      // 在开发环境使用模拟数据，生产环境使用真实接口
      const response =
        process.env.NODE_ENV === 'development'
          ? await mockPerformanceData(defaultParams)
          : await queryPerformanceTarget(defaultParams);

      if (response?.performanceDataList) {
        setPerformanceData(response.performanceDataList);
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('获取绩效数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 时间筛选变化
  const onValuesChange = (values: any) => {
    // eslint-disable-next-line no-console
    console.log('时间筛选变化:', values, '全局筛选条件:', filterOptions);
    if (values.queryDate) {
      fetchPerformanceData({ queryDate: values.queryDate });
    }
  };

  // 处理指标卡片点击
  const handleIndicatorSelect = (indicatorCode: string) => {
    setSelectedIndicator(indicatorCode);
  };

  // 初始化数据
  useEffect(() => {
    fetchPerformanceData();
  }, []);

  const extraRender = () => {
    return (
      <Form onValuesChange={onValuesChange} form={form} layout="inline">
        <Form.Item name="queryDate" label="" initialValue="20250228">
          <Select
            style={{ width: 120 }}
            options={[
              { label: '今日', value: '20250228' },
              { label: '昨日', value: '20250227' },
              { label: '本周', value: '20250224' },
              { label: '本月', value: '20250201' },
            ]}
          />
        </Form.Item>
      </Form>
    );
  };

  const titleRender = () => {
    return (
      <div className={styles.titleContainer}>
        <span className={styles.title}>绩效目标</span>
        <span className={styles.disclaimer}>绩效数据仅组织下绩效组和营销组队内参考</span>
      </div>
    );
  };

  return (
    <Card
      title={titleRender()}
      extra={extraRender()}
      bodyStyle={{ padding: '16px 24px' }}
      loading={loading}
    >
      <Flex gap={24} vertical>
        {/* 绩效指标卡片 */}
        <PerformanceMetrics
          data={performanceData}
          selectedIndicator={selectedIndicator}
          onIndicatorSelect={handleIndicatorSelect}
        />

        {/* 绩效指标&完成度图表 */}
        <PerformanceChart data={performanceData} selectedIndicator={selectedIndicator} />
      </Flex>
    </Card>
  );
};

export default Performance;
