# 绩效组件 (Performance)

## 概述

绩效组件实现了管理者作战地图中的绩效目标展示功能，包含绩效指标卡片和绩效完成度图表两个主要部分。

## 组件结构

```
Performance/
├── index.tsx                    # 主组件
├── index.module.less           # 主组件样式
├── types/
│   └── index.ts                # 类型定义
├── services/
│   └── performanceService.ts   # API服务
└── components/
    ├── PerformanceMetrics/     # 绩效指标卡片组件
    │   ├── index.tsx
    │   ├── index.module.less
    │   └── PerformanceMetricCard.tsx
    └── PerformanceChart/       # 绩效图表组件
        ├── index.tsx
        └── index.module.less
```

## 功能特性

### 1. 绩效指标卡片 (PerformanceMetrics)

- 使用 StatisticCard 组件实现，参照 cycleCard 的写法
- 展示 8 个主要绩效指标（总收入、广告收入、年费收入、CPS 收入、智能体收入、首充率、留存率、GMV）
- 主卡片（总收入）有特殊样式和 icon-rise 标识
- 显示目标完成度、月环比、周环比趋势
- 支持趋势图标（上升/下降）和颜色标识

### 2. 绩效图表 (PerformanceChart)

- 展示团队/人员绩效对比柱状图
- 支持指标选择（下拉菜单）
- 支持对比类型选择（日环比、周环比、月环比）
- 分页显示数据
- 显示免责声明
- 支持图表交互（tooltip、标签等）

## API 接口

### 1. 绩效目标查询

- 接口：com.amap.sales.operation.app.hsf.PerformanceQueryFacadeImpl.queryPerformanceTarget
- 用途：获取绩效指标数据

### 2. 绩效图表数据查询

- 接口：com.amap.sales.operation.app.hsf.PerformanceQueryFacadeImpl.queryPerformanceDataComparison
- 用途：获取绩效对比图表数据

## 技术实现

- 使用 @ant-design/pro-components 的 StatisticCard 组件
- 遵循项目现有的 cycleCard 组件设计模式
- 支持开发模式下的模拟数据
- 完整的 TypeScript 类型支持
- 响应式布局设计

## 注意事项

1. 组件已集成到管理者作战地图主页面
2. 支持筛选条件联动（时间选择等）
3. 图表数据支持分页显示
4. 包含完整的错误处理和加载状态
5. 遵循项目的 linting 规范
