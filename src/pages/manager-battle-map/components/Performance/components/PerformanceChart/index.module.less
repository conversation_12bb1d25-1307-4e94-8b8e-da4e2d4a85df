.chartCard {
  border-radius: 8px;
  border: 1px solid #f0f0f0;

  :global {
    .ant-card-head-title {
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.chartContainer {
  margin: 16px 0;

  // 确保图表容器有合适的高度
  min-height: 320px;

  :global {
    // 自定义图表样式
    .g2-tooltip {
      background: rgba(0, 0, 0, 0.75);
      border-radius: 4px;
      color: #fff;
      padding: 8px 12px;
      font-size: 12px;
    }

    // 图例样式
    .g2-legend {
      margin-bottom: 16px;
    }

    // 柱状图样式
    .g2-geometry-interval {
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

.chartNote {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.noteText {
  margin: 0;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 20px;
}

.emptyState {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.disclaimer {
  margin-bottom: 16px;

  :global {
    .ant-alert-message {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}

.paginationContainer {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;

  :global {
    .ant-pagination {
      .ant-pagination-item {
        border-radius: 4px;
      }

      .ant-pagination-item-active {
        border-color: #1890ff;
      }

      .ant-pagination-total-text {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}
