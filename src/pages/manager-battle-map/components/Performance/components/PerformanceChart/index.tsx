/**
 * 绩效指标&完成度图表组件
 */

import React, { useState, useEffect, useMemo } from 'react';
import { Card, Select, Space, Pagination, Alert } from 'antd';
import { Column } from '@ant-design/plots';
import {
  queryPerformanceDataComparison,
  mockPerformanceChartData,
} from '../../services/performanceService';
import type {
  PerformanceChartProps,
  BarChartData,
  PerformanceChartQueryParams,
  PageInfo,
} from '../../types';

import styles from './index.module.less';

const PerformanceChart: React.FC<PerformanceChartProps> = ({
  data,
  selectedIndicator: propSelectedIndicator,
}) => {
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState<BarChartData[]>([]);
  const [pageInfo, setPageInfo] = useState<PageInfo>({
    totalCount: 0,
    pageNo: 1,
    pageSize: 10,
    hasMore: false,
  });
  const [disclaimer, setDisclaimer] = useState<string>('');
  const [selectedIndicator, setSelectedIndicator] = useState<string>(
    propSelectedIndicator || 'TOTAL_INCOME',
  );
  const [comparisonType, setComparisonType] = useState<string>('DAY_ON_DAY');

  // 获取图表数据
  const fetchChartData = async (params?: Partial<PerformanceChartQueryParams>) => {
    setLoading(true);
    try {
      const defaultParams: PerformanceChartQueryParams = {
        pageSource: 'MANAGER_PAGE',
        positionId: 'POS001',
        staffId: 'STAFF001',
        companyId: 'COMP001',
        queryDate: '20240115',
        comparisonPeriodType: comparisonType,
        pageInfo: {
          pageNo: pageInfo.pageNo,
          pageSize: pageInfo.pageSize,
        },
        jumpSource: 'HISTOGRAM_CLICK',
        selectedIndicator,
        commonOperatorInfo: {
          operatorId: 'OP001',
          operatorName: '张三',
        },
        ...params,
      };

      // 在开发环境使用模拟数据
      const response =
        process.env.NODE_ENV === 'development'
          ? await mockPerformanceChartData(defaultParams)
          : await queryPerformanceDataComparison(defaultParams);

      if (response) {
        setChartData(response.barChartDataList || []);
        setPageInfo(response.pageInfo);
        setDisclaimer(response.disclaimer || '');
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('获取绩效图表数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 转换数据格式用于图表显示
  const plotData = useMemo(() => {
    if (!chartData || chartData.length === 0) return [];

    return chartData.map((item) => ({
      name: item.staffName,
      team: item.teamName,
      value: parseFloat(item.currentValue) || 0,
      fullName: `${item.staffName}(${item.teamName})`,
    }));
  }, [chartData]);

  // 监听外部传入的选中指标变化
  useEffect(() => {
    if (propSelectedIndicator && propSelectedIndicator !== selectedIndicator) {
      setSelectedIndicator(propSelectedIndicator);
    }
  }, [propSelectedIndicator]);

  // 初始化数据和监听指标变化
  useEffect(() => {
    fetchChartData();
  }, [selectedIndicator, comparisonType]);

  // 分页变化
  const handlePageChange = (page: number, size: number) => {
    setPageInfo((prev) => ({ ...prev, pageNo: page, pageSize: size }));
    fetchChartData({
      pageInfo: { pageNo: page, pageSize: size },
    });
  };

  // 指标变化
  const handleIndicatorChange = (value: string) => {
    setSelectedIndicator(value);
    setPageInfo((prev) => ({ ...prev, pageNo: 1 })); // 重置到第一页
  };

  // 对比类型变化
  const handleComparisonChange = (value: string) => {
    setComparisonType(value);
    setPageInfo((prev) => ({ ...prev, pageNo: 1 })); // 重置到第一页
  };

  // 图表配置
  const config = {
    data: plotData,
    xField: 'fullName',
    yField: 'value',
    height: 350,
    scale: {
      y: {
        domainMin: 0,
        nice: true,
      },
    },
    axis: {
      x: {
        labelFormatter: (text: string) => {
          // 限制标签长度，避免重叠
          return text.length > 8 ? `${text.slice(0, 8)}...` : text;
        },
        labelRotate: -45, // 旋转标签避免重叠
      },
      y: {
        grid: true,
        gridStrokeOpacity: 0.3,
        labelFormatter: (val: number) => `${val}`,
      },
    },
    // 添加标签显示数值
    label: {
      formatter: (datum: any) => {
        return `${datum.value}`;
      },
      style: {
        fontSize: 12,
        fontWeight: 'bold',
      },
    },
    // 添加提示信息
    tooltip: {
      formatter: (datum: any) => ({
        name: datum.fullName,
        value: datum.value,
      }),
    },
    // 柱子样式
    style: {
      radiusTopLeft: 4,
      radiusTopRight: 4,
      fill: '#1890ff',
    },
  };

  // 指标选项
  const indicatorOptions = useMemo(() => {
    if (!data || data.length === 0) return [];
    return data.map((item) => ({
      label: item.indicatorName,
      value: item.indicatorCode,
    }));
  }, [data]);

  // 对比类型选项
  const comparisonOptions = [
    { label: '日环比', value: 'DAY_ON_DAY' },
    { label: '周环比', value: 'WEEK_ON_WEEK' },
    { label: '月环比', value: 'MONTH_ON_MONTH' },
  ];

  return (
    <Card
      title="绩效指标&完成度"
      className={styles.chartCard}
      loading={loading}
      extra={
        <Space>
          <Select
            value={selectedIndicator}
            onChange={handleIndicatorChange}
            options={indicatorOptions}
            style={{ width: 120 }}
            placeholder="选择指标"
          />
          <Select
            value={comparisonType}
            onChange={handleComparisonChange}
            options={comparisonOptions}
            style={{ width: 100 }}
          />
        </Space>
      }
    >
      <div className={styles.chartContainer}>
        {plotData && plotData.length > 0 ? (
          <Column {...config} />
        ) : (
          <div className={styles.emptyState}>
            <p>暂无绩效数据</p>
          </div>
        )}
      </div>

      {/* 分页器 */}
      {pageInfo.totalCount > 0 && (
        <div className={styles.paginationContainer}>
          <Pagination
            current={pageInfo.pageNo}
            total={pageInfo.totalCount}
            pageSize={pageInfo.pageSize}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`}
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
          />
        </div>
      )}
    </Card>
  );
};

export default PerformanceChart;
