/**
 * 单个绩效指标卡片
 */

import React from 'react';
import { Typography, Space, Tooltip, Progress } from 'antd';
import { RiseOutlined, FallOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { StatisticCard } from '@ant-design/pro-components';
import type { PerformanceMetricCardProps } from '../../types';

import styles from './index.module.less';

const { Text } = Typography;
const { Statistic } = StatisticCard;

const PerformanceMetricCard: React.FC<PerformanceMetricCardProps> = ({
  data,
  isMainCard = false,
  isSelected = false,
  onClick,
}) => {
  const {
    indicatorName,
    indicatorValue,
    unit,
    targetValue,
    targetCompletionRate,
    monthOnMonthRate,
    weekOnWeekRate,
  } = data;

  // 格式化数值显示
  const formatValue = (value: string) => {
    if (!value || value === '0.0') {
      return '0';
    }
    return value;
  };

  // 获取趋势图标和样式
  const getTrendIcon = (rate: string) => {
    if (!rate || rate === '0%') return null;

    const isPositive = !rate.startsWith('-');
    const isRed = rate.startsWith('^');

    let color = '#F5222D'; // 默认红色
    if (isRed) {
      color = '#F5222D';
    } else if (isPositive) {
      color = '#52C41A';
    }
    return {
      icon: isPositive ? <RiseOutlined /> : <FallOutlined />,
      color,
      value: rate.replace('^', ''),
    };
  };

  const monthTrend = getTrendIcon(monthOnMonthRate);
  const weekTrend = getTrendIcon(weekOnWeekRate);
  const formattedValue = formatValue(indicatorValue);

  // 标题渲染
  const titleRender = () => {
    return (
      <div className={styles.cardTitle}>
        <span>{indicatorName}</span>
        <Tooltip title={`${indicatorName}指标说明`}>
          <QuestionCircleOutlined className={styles.helpIcon} />
        </Tooltip>
      </div>
    );
  };

  // 计算进度条百分比
  const getProgressPercent = () => {
    const completion = parseFloat(targetCompletionRate.replace('%', '')) || 0;
    return Math.min(completion, 100); // 限制最大值为100%
  };

  // 趋势描述渲染
  const descriptionRender = () => {
    return (
      <Space direction="vertical" size={8} className={styles.trendContainer}>
        {/* 进度条 */}
        {isMainCard && (
          <Progress
            percent={getProgressPercent()}
            showInfo={false}
            strokeColor="#1890ff"
            trailColor="#f0f0f0"
            size="small"
          />
        )}

        {/* 趋势数据 */}
        <Space size={16}>
          {monthTrend && (
            <Statistic
              title={<span className={styles.trendLabel}>月环比</span>}
              prefix={monthTrend.icon}
              value={monthTrend.value}
              valueStyle={{ color: monthTrend.color, fontSize: 12 }}
            />
          )}
          {weekTrend && (
            <Statistic
              title={<span className={styles.trendLabel}>周环比</span>}
              prefix={weekTrend.icon}
              value={weekTrend.value}
              valueStyle={{ color: weekTrend.color, fontSize: 12 }}
            />
          )}
        </Space>
      </Space>
    );
  };

  return (
    <div
      className={`${styles.metricCard} ${isSelected ? styles.selectedCard : ''}`}
      onClick={onClick}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      <StatisticCard
        bordered
        hoverable
        title={titleRender()}
        statistic={{
          value: formattedValue,
          suffix: <span className={styles.unit}>{unit}</span>,
          description: descriptionRender(),
        }}
        extra={
          isMainCard ? (
            <div className={styles.targetInfo}>
              <div className={styles.targetRow}>
                <Text className={styles.targetLabel}>目标完成度</Text>
                <Text className={styles.targetValue}>{targetCompletionRate}</Text>
              </div>
              <Text className={styles.targetDetail}>
                目标 <span className={styles.targetNumber}>{targetValue}</span>
              </Text>
            </div>
          ) : null
        }
        className={isMainCard ? styles.mainStatisticCard : styles.normalStatisticCard}
        style={{
          height: 180, // 调整为更高的尺寸
          background: '#fff',
        }}
        headStyle={{
          padding: '16px 16px 0',
          borderBottom: 'none',
          minHeight: 'auto',
        }}
        bodyStyle={{
          padding: '16px 16px 20px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          flex: 1,
        }}
      />
    </div>
  );
};

export default PerformanceMetricCard;
