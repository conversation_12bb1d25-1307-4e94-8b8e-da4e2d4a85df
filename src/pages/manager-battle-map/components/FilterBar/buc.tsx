import React, { useEffect, useCallback, useState } from 'react';
import { Select, Form } from 'antd';
import { useRequest } from 'ahooks';
import afetch from '@alife/amap-fetch';
import JobTree from '@alife/mo-job-tree-select';
import type { FilterOptions } from '@/types';
import { EntityType } from '@/types';
import type { UserInfo } from '../../types';
import { getEnv, Env } from '@alife/amap-mp-utils';

// 预发产技测试临时 30234568
// 线上渠道商家成长部 560102013
const CHANNEL_BUSINESS_DEPT_ID = getEnv() === Env.PRE ? '30234568' : '560102013';

/**
 * 组件Props接口
 */
interface BucFilterProps {
  /** 筛选条件变化回调 */
  onFilterChange: (filterOptions: FilterOptions) => void;
  /** 初始值 */
  defaultValue?: Partial<FilterOptions>;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * BUC筛选组件 - 用于内部员工
 *
 * ## 功能说明
 *
 * ### 主要功能
 * 1. **组织架构选择**: 使用 @alife/mo-job-tree-select 组件进行组织架构选择
 * 2. **条件联动**: 根据组织架构选择结果动态显示后续筛选项
 * 3. **渠道业务部判断**: 检查选中节点路径是否包含"渠道业务部"
 * 4. **用户筛选**: 展示组织架构选择结果的extra.userId列表
 * 5. **服务商查询**: 调用searchAgent接口获取服务商列表
 * 6. **筛选联动**: 最终触发全局filterOptions变化
 *
 * ### 交互逻辑
 * ```
 * 组织架构选择 → 判断路径是否包含"渠道业务部"
 *                    ↓
 *                包含 → 显示用户选择框 → 显示服务商选择框
 *                    ↓                    ↓
 *               选择用户 → 调用searchAgent → 选择服务商 → 触发filterOptions
 *                    ↓
 *               不包含 → 直接触发filterOptions
 * ```
 */
const BucFilter: React.FC<BucFilterProps> = ({
  onFilterChange,
  defaultValue,
  disabled = false,
}) => {
  const [form] = Form.useForm();

  // 使用 useWatch 监听表单值变化
  const organizationValue = Form.useWatch('organization', form);
  const userIdValue = Form.useWatch('userId', form);

  // 通过 useRef 计算是否显示选择器
  const showUserSelect =
    organizationValue?.node?.value?.includes(CHANNEL_BUSINESS_DEPT_ID) &&
    organizationValue?.node?.leafJob === true;
  const showAgentSelect = showUserSelect && userIdValue;
  const [userOptions, setUserOptions] = useState<UserInfo[]>([]);

  /**
   * 服务商查询请求
   */
  const {
    data: agentOptions = [],
    loading: agentLoading,
    run: searchAgents,
  } = useRequest(
    async (userId: string) => {
      if (!userId) return [];

      try {
        // 调用searchAgent接口获取服务商列表
        const result = await afetch({
          params: {
            action: 'alsc-kbt-merchant-admin.AlscAgentGateWayWeb.queryAlscAgentCompanyByParams',
            bizContent: {
              operationOsUserId: userId,
            },
          },
        });

        // 转换数据格式为组件需要的格式
        return result?.data?.data?.result || [];
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('查询服务商列表失败:', error);
        return [];
      }
    },
    {
      manual: true,
      onError: (error) => {
        // eslint-disable-next-line no-console
        console.error('searchAgent接口调用失败:', error);
      },
    },
  );

  /**
   * 表单字段变化监听
   */
  const handleFieldsChange = useCallback(
    (changedFields: any[]) => {
      console.log('changedFields', changedFields);
      changedFields.forEach((field) => {
        const { name, value } = field;

        if (name[0] === 'organization') {
          form.setFieldsValue({ userId: undefined, agentId: undefined });
          // 统一设置用户选项并触发筛选
          const members = value?.node?.members || [];
          const userList: UserInfo[] = members.map((member: any) => ({
            userId: member.userId || member.id,
            displayName: member.displayName,
          }));
          setUserOptions(userList || []);
          onFilterChange({
            jobIds: value?.value ? [String(value.value)] : undefined,
            entityType: EntityType.JOB,
            jobLevel: value?.node?.jobPath?.split('/').filter((item) => !!item).length,
          });
        } else if (name[0] === 'userId') {
          form.setFieldsValue({ agentId: undefined });
          const userId = value;
          searchAgents(userId);
          const formValues = form.getFieldsValue();
          const job = formValues?.organization;
          onFilterChange({
            jobIds: job?.value ? [String(job.value)] : undefined,
            channelOptStaffIds: userId ? [String(userId)] : undefined,
            entityType: EntityType.CHANNEL_OPT,
            jobLevel: job?.node?.jobPath?.split('/').filter((item) => !!item).length,
          });
        } else if (name[0] === 'agentId') {
          const formValues = form.getFieldsValue();
          const job = formValues?.organization;
          onFilterChange({
            channelCompanyIds: formValues.agentId ? [String(formValues.agentId)] : undefined,
            channelOptStaffIds: formValues.userId ? [String(formValues.userId)] : undefined,
            jobIds: job?.value ? [String(job.value)] : undefined,
            entityType: EntityType.COM,
            jobLevel: job?.node?.jobPath?.split('/').filter((item) => !!item).length,
          });
        }
      });
    },
    [form, searchAgents, onFilterChange, setUserOptions],
  );

  /**
   * 初始化默认值
   */
  // useEffect(() => {
  //   if (defaultValue) {
  //     form.setFieldsValue({
  //       organization:
  //         Array.isArray(defaultValue.jobIds) && defaultValue.jobIds[0]
  //           ? { value: defaultValue.jobIds[0] }
  //           : undefined,
  //       userId: Array.isArray(defaultValue.channelOptStaffIds)
  //         ? defaultValue.channelOptStaffIds[0]
  //         : undefined,
  //       agentId: Array.isArray(defaultValue.channelCompanyIds)
  //         ? defaultValue.channelCompanyIds[0]
  //         : undefined,
  //     });
  //   }
  // }, [defaultValue, form]);

  const handleLoadFinish = useCallback((data: any) => {
    const organization = {
      value: data[0].value,
      title: data[0].title,
      node: data[0],
    };
    form.setFieldsValue({
      organization,
    });
    handleFieldsChange([{ name: ['organization'], value: organization }]);
  }, []);

  return (
    <Form form={form} layout="inline" onFieldsChange={handleFieldsChange}>
      <div style={{ display: 'flex' }}>
        {/* 组织架构选择 - 使用 @alife/mo-job-tree-select */}
        <Form.Item name="organization">
          <JobTree
            style={{ width: 300 }}
            onlyUserJob
            multiple={false}
            onFirstLoaded={handleLoadFinish}
          />
        </Form.Item>

        {/* 用户选择框 - 仅在包含渠道业务部时显示 */}
        {showUserSelect && (
          <Form.Item name="userId">
            <Select
              style={{ width: 240 }}
              placeholder="选择小二"
              allowClear
              disabled={disabled}
              options={userOptions.map((user) => ({
                value: user.userId,
                label: user.displayName,
                title: `${user.displayName}(${user.userId})`,
              }))}
              showSearch
              filterOption={(input, option) =>
                (option?.label?.toString() || '').toLowerCase().includes(input.toLowerCase()) ||
                (option?.title?.toString() || '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
        )}

        {/* 服务商选择框 - 仅在选择用户后显示 */}
        {showAgentSelect && (
          <Form.Item name="agentId">
            <Select
              style={{ width: 240 }}
              placeholder="选择服务商"
              allowClear
              disabled={disabled}
              loading={agentLoading}
              options={agentOptions.map((agent) => ({
                value: agent.companyId,
                label: agent.companyName,
              }))}
              showSearch
              filterOption={(input, option) =>
                (option?.label?.toString() || '').toLowerCase().includes(input.toLowerCase())
              }
              notFoundContent={agentLoading ? '加载中...' : '暂无数据'}
            />
          </Form.Item>
        )}
      </div>
    </Form>
  );
};

export default BucFilter;
