/**
 * 任务明细
 */

import React, { useState } from 'react';
import { Button, Table, Select, Input, Pagination, Spin } from 'antd';
import styled from 'styled-components';
import { useRequest } from 'ahooks';
import BatchTaskDrawer from './components/batch-task-drawer';
import TaskDistributeForm from './components/task-distribute-form';
import { queryTaskSummary, queryTaskDetail } from '@/services';

const { Option } = Select;

const PageContainer = styled.div`
  padding: 24px;
  background: #fff;
  min-height: 100vh;
`;

const HeaderSection = styled.div`
  margin-bottom: 24px;
`;

const TabContainer = styled.div`
  display: flex;
  margin-bottom: 16px;
`;

const TabItem = styled.div<{ active?: boolean }>`
  padding: 8px 16px;
  margin-right: 8px;
  cursor: pointer;
  border-radius: 4px;
  color: ${(props) => (props.active ? '#1890ff' : '#666')};
  background: ${(props) => (props.active ? '#e6f7ff' : 'transparent')};
  border: 1px solid ${(props) => (props.active ? '#1890ff' : '#d9d9d9')};
  transition: all 0.3s;

  &:hover {
    color: #1890ff;
    border-color: #1890ff;
  }
`;

const FilterContainer = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
`;

const FilterItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const BatchButton = styled(Button)`
  margin-left: auto;
`;

const TableContainer = styled.div`
  .ant-table-thead > tr > th {
    background: #fafafa;
    font-weight: 600;
  }
`;

const PaginationContainer = styled.div`
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
`;

const TaskSummary: React.FC = () => {
  const [activeTab, setActiveTab] = useState('summary');
  const [batchDrawerVisible, setBatchDrawerVisible] = useState(false);
  const [distributeFormVisible, setDistributeFormVisible] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState({
    teamId: undefined,
    deptId: undefined,
    employeeName: undefined,
  });

  // 获取任务汇总数据
  const {
    data: summaryData,
    loading: summaryLoading,
    refresh: refreshSummary,
  } = useRequest(
    () =>
      queryTaskSummary({
        deptIds: filters.deptId ? [filters.deptId] : undefined,
        staffIds: filters.employeeName ? [filters.employeeName] : undefined,
        entityType: 'DEPARTMENT',
      }),
    {
      refreshDeps: [filters],
      onError: (error) => {
        // eslint-disable-next-line no-console
        console.error('获取任务汇总失败:', error);
      },
    },
  );

  // 获取任务明细数据
  const {
    data: detailData,
    loading: detailLoading,
    refresh: refreshDetail,
  } = useRequest(
    () =>
      queryTaskDetail({
        deptIds: filters.deptId ? [filters.deptId] : undefined,
        staffIds: filters.employeeName ? [filters.employeeName] : undefined,
        entityType: 'DEPARTMENT',
        pageNo: currentPage,
        pageSize,
      }),
    {
      refreshDeps: [filters, currentPage, pageSize],
      onError: (error) => {
        // eslint-disable-next-line no-console
        console.error('获取任务明细失败:', error);
      },
    },
  );

  const handleBatchTask = () => {
    setBatchDrawerVisible(true);
  };

  const handleBatchDrawerClose = () => {
    setBatchDrawerVisible(false);
  };

  const handleDistributeTask = () => {
    setDistributeFormVisible(true);
  };

  const handleDistributeFormClose = () => {
    setDistributeFormVisible(false);
  };

  const handleDistributeSuccess = () => {
    // 任务下发成功后刷新列表数据
    refreshSummary();
    refreshDetail();
  };

  const handleTaskDistributed = () => {
    // 任务下发成功后刷新任务下发列表
    setRefreshTrigger((prev) => prev + 1);
  };

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) {
      setPageSize(size);
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSearch = () => {
    refreshSummary();
    refreshDetail();
  };

  const summaryColumns = [
    {
      title: '团队',
      dataIndex: 'deptName',
      key: 'deptName',
      width: 200,
      render: (text: string, record: any) =>
        text || record.bdBizLine || record.companyName || record.aliCode,
    },
    {
      title: '任务下发量',
      dataIndex: 'numOfTask',
      key: 'numOfTask',
      width: 120,
    },
    {
      title: '任务完成量',
      dataIndex: 'numOfFinishTask',
      key: 'numOfFinishTask',
      width: 120,
    },
    {
      title: '任务完成率',
      dataIndex: 'ratioOfFinishTask',
      key: 'ratioOfFinishTask',
      width: 120,
      render: (value: string) => value || '-',
    },
    {
      title: '主管催办量',
      dataIndex: 'numOfFollowUpDueTask',
      key: 'numOfFollowUpDueTask',
      width: 120,
    },
    {
      title: '系统推荐量',
      dataIndex: 'numOfRecTask',
      key: 'numOfRecTask',
      width: 120,
    },
    {
      title: '平均完成时效',
      dataIndex: 'timeRangeOfTask',
      key: 'timeRangeOfTask',
      width: 120,
      render: (value: string) => value || '-',
    },
  ];

  const detailColumns = [
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 200,
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskType',
      width: 120,
    },
    {
      title: '任务来源',
      dataIndex: 'taskSource',
      key: 'taskSource',
      width: 120,
    },
    {
      title: '下发日期',
      dataIndex: 'taskPublishDate',
      key: 'taskPublishDate',
      width: 120,
    },
    {
      title: '催办日期',
      dataIndex: 'taskFollowUpDueDate',
      key: 'taskFollowUpDueDate',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      width: 100,
    },
    {
      title: '完成日期',
      dataIndex: 'taskFinishDate',
      key: 'taskFinishDate',
      width: 120,
      render: (text: string) => text || '-',
    },
    {
      title: '完成时效',
      dataIndex: 'timeRangeOfTask',
      key: 'timeRangeOfTask',
      width: 100,
      render: (value: number) => (value ? `${value}天` : '-'),
    },
  ];

  return (
    <PageContainer>
      <HeaderSection>
        <TabContainer>
          <TabItem active={activeTab === 'summary'} onClick={() => setActiveTab('summary')}>
            任务汇总
          </TabItem>
          <TabItem active={activeTab === 'detail'} onClick={() => setActiveTab('detail')}>
            任务明细
          </TabItem>
        </TabContainer>

        <FilterContainer>
          <FilterItem>
            <span>按团队筛选</span>
            <Select
              placeholder="请选择团队"
              style={{ width: 150 }}
              value={filters.teamId}
              onChange={(value) => handleFilterChange('teamId', value)}
              allowClear
            >
              <Option value="team1">电销团队</Option>
              <Option value="team2">美食&休娱团队</Option>
            </Select>
          </FilterItem>
          <FilterItem>
            <span>按部门筛选</span>
            <Select
              placeholder="请选择部门"
              style={{ width: 150 }}
              value={filters.deptId}
              onChange={(value) => handleFilterChange('deptId', value)}
              allowClear
            >
              <Option value="dept1">部门1</Option>
              <Option value="dept2">部门2</Option>
            </Select>
          </FilterItem>
          <FilterItem>
            <span>小二名称</span>
            <Input
              placeholder="请输入小二名称"
              style={{ width: 150 }}
              value={filters.employeeName}
              onChange={(e) => handleFilterChange('employeeName', e.target.value)}
            />
          </FilterItem>
          <Button onClick={handleSearch}>查询</Button>
          <BatchButton type="primary" onClick={handleBatchTask}>
            批量下发任务
          </BatchButton>
        </FilterContainer>
      </HeaderSection>

      <TableContainer>
        <Spin spinning={activeTab === 'summary' ? summaryLoading : detailLoading}>
          <Table
            columns={activeTab === 'summary' ? summaryColumns : detailColumns}
            dataSource={activeTab === 'summary' ? summaryData?.dataList : detailData?.dataList}
            pagination={false}
            scroll={{ x: 1000 }}
            expandable={
              activeTab === 'summary'
                ? {
                    defaultExpandAllRows: false,
                    expandRowByClick: false,
                  }
                : undefined
            }
            rowKey={(record, index) => record.key || index}
          />
        </Spin>
      </TableContainer>

      <PaginationContainer>
        <Pagination
          current={currentPage}
          total={
            activeTab === 'summary'
              ? summaryData?.dataList?.length || 0
              : detailData?.pageInfo?.totalCount || 0
          }
          pageSize={pageSize}
          showSizeChanger
          showQuickJumper
          onChange={handlePageChange}
          onShowSizeChange={handlePageChange}
        />
      </PaginationContainer>

      <BatchTaskDrawer
        visible={batchDrawerVisible}
        onClose={handleBatchDrawerClose}
        onDistributeTask={handleDistributeTask}
        refreshTrigger={refreshTrigger}
      />

      <TaskDistributeForm
        visible={distributeFormVisible}
        onClose={handleDistributeFormClose}
        onSuccess={handleDistributeSuccess}
        onTaskDistributed={handleTaskDistributed}
      />
    </PageContainer>
  );
};

export default TaskSummary;
