import request, { gdRequest } from './request';

/**
 * 门店仪表盘
 */
export const queryMerchantShopDashboard = ({
  params,
}: {
  params?: any;
} = {}) => {
  return request('amap-sales-automation.FireMapsFacade.queryMerchantShopDashboard', {
    params,
  });
};

/**
 * 诊断建议展示(催办弹窗)
 */
export const queryEmployeeAnalysis = ({
  params,
}: {
  params?: string;
} = {}): Promise<{
  materials: Array<{ employeeId: string }>;
}> => {
  return request(
    'com.amap.sales.operation.client.AgentOperationQueryFacade.queryEmployeeAnalysis',
    { params },
  );
};

/**
 * 任务催办(催办弹窗)
 */
export const queryOptTodoTask = ({
  params,
}: {
  params?: any;
} = {}) => {
  return request('com.amap.sales.operation.client.OptTaskManageFacade.queryOptTodoTask', {
    params,
  });
};

/**
 * 任务催办提交(催办弹窗)
 */
export const remindOptTask = ({
  params,
}: {
  params?: any;
} = {}) => {
  return request('com.amap.sales.operation.client.OptTaskManageFacade.remindOptTask', {
    params,
  });
};
